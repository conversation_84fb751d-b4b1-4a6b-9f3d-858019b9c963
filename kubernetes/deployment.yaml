apiVersion: apps/v1
kind: Deployment
metadata:
  name: company-search-api
  labels:
    app: company-search-api
    version: v1
spec:
  minReadySeconds: 5
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 60
  strategy:
    rollingUpdate:
      maxUnavailable: 0
    type: RollingUpdate
  selector:
    matchLabels:
      app: company-search-api
      version: v1
  template:
    metadata:
      annotations:
        prometheus.io/scrape: 'true'
      labels:
        app: company-search-api
        version: v1
    spec:
      containers:
        - name: company-search-api
          image: IMAGE_NAME:IMAGE_TAG
          imagePullPolicy: IfNotPresent
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: SERVICE_NAME
              value: 'company-search-api'
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: ALIYUN_LOGTAIL_USER_DEFINED_ID
              value: 'company-search-api'
            - name: SENTRY_DSN
              value: CI:SENTRY_DSN
            - name: SENTRY_ENVIRONMENT
              value: CI:SENTRY_ENVIRONMENT
            - name: SENTRY_RELEASE
              value: CI:SENTRY_RELEASE
            - name: NODE_ENV
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: node.env
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: redis.host
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: redis.port
            - name: REDIS_DB
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: redis.db
            - name: REDIS_PASSWD
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: redis.password
            - name: ES_COMPANY_NODES
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: es.company.nodes
            - name: ES_COMPANY_INDEX
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: es.company.index
            - name: KEYWORD_ANALYZE
              valueFrom:
                configMapKeyRef:
                  name: search-env-base
                  key: keyword-analyze
          ports:
            - containerPort: 7001
          livenessProbe:
            httpGet:
              path: /company/ping
              port: 7001
            initialDelaySeconds: 10
            timeoutSeconds: 10
            periodSeconds: 30
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /company/ping
              port: 7001
            initialDelaySeconds: 10
            timeoutSeconds: 5
            periodSeconds: 15
            failureThreshold: 3
          resources:
            limits:
              cpu: 1000m
              memory: 1000Mi
            requests:
              cpu: 100m
              memory: 256Mi
          volumeMounts:
            - name: varlog
              mountPath: /app/logs
        - name: log-agent
          image: harbor-in.greatld.com/kezhaozhao/fluentd-kzz:1.2
          imagePullPolicy: Always
          env:
            - name: FLUENTD_ARGS
              value: -c /fluentd/etc/fluent.conf
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: SERVICE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['app']
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          volumeMounts:
            - name: varlog
              mountPath: /app/logs
            - name: config-volume
              mountPath: /fluentd/etc
      volumes:
        - name: varlog
          emptyDir: {}
        - name: config-volume
          configMap:
            name: fluentd-config

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: company-search-api
  name: company-search-api
spec:
  ports:
    - name: http
      port: 7001
      protocol: TCP
      targetPort: 7001
  selector:
    app: company-search-api
  type: ClusterIP
