import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { AxiosRequestConfig, Method } from 'axios';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class HttpUtilsService {
  private readonly logger: Logger = QccLogger.getLogger(HttpUtilsService.name);

  constructor(private readonly httpService: HttpService) {}

  public async httpRequest(method: Method, url: string, data: Record<string, any>) {
    const requestParams: AxiosRequestConfig = {
      timeout: 200,
      method: method,
      url: url,
      data: data,
    };
    return this.sendResquest(requestParams);
  }

  public async getHttpRequest(url: string, data: Record<string, any>) {
    const requestParams: AxiosRequestConfig = {
      method: 'GET',
      url: url,
      params: data,
    };
    return this.sendResquest(requestParams);
  }

  private sendResquest(requestParams: AxiosRequestConfig) {
    return this.httpService
      .request(requestParams)
      .toPromise()
      .then((res) => {
        return res.data;
      })
      .catch((error) => {
        this.logger.error(error);
        // throw new InternalServerErrorException({
        //   ...CommonExceptions.Common.QccService.Error,
        //   internalMessage: error.message
        // });
      });
  }
}
