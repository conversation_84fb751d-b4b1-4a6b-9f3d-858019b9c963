import { Injectable } from '@nestjs/common';
import * as dotenvx from '@dotenvx/dotenvx';

@Injectable()
export class ConfigService {
  domain: string;
  nodeEnv: string;
  esConfig: any;
  axiosConfig: any;

  constructor() {
    this.nodeEnv = process.env.NODE_ENV || 'local';
    if (this.nodeEnv == 'local' || process.env.JEST_WORKER_ID) {
      // 开发本地环境通过.env加载默认配置
      dotenvx.config();
    }
    this.domain = process.env.DOMAIN || 'search.dev.greatld.com';
    this.axiosConfig = {
      timeout: 10000, // 10s
      headers: {
        'x-kzz-request-from-server': 'company-search-api',
        'x-request-from-head-app-name': 'company-search-api',
      },
    };
    this.esConfig = {
      kysCompany: {
        nodes: process.env.ES_COMPANY_NODES,
        indexName: process.env.ES_COMPANY_INDEX || 'kys_company_query',
        indexType: '_doc',
        headers: {
          'x-request-from-head-app-name': 'kzz-qcc-tender-service',
        },
      },
    };
  }
}
