import { Global, Module } from '@nestjs/common';
import { ConfigService } from './config.service';
import { HttpModule } from '@nestjs/axios';

@Global()
@Module({
  providers: [
    {
      provide: ConfigService,
      useValue: new ConfigService(),
    },
  ],
  imports: [
    HttpModule.registerAsync({
      useFactory: async (configService: ConfigService) => configService.axiosConfig,
      inject: [ConfigService],
    }),
  ],
  exports: [ConfigService, HttpModule],
})
export class ConfigModule {}
