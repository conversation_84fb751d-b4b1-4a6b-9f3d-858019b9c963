import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Logger } from 'log4js';
import { CommonExceptions } from './exceptions/exceptionConstants';
import { SearchExceptionResponse } from './exceptions/model';

// import { SentryHelper } from './sentry/SentryHelper';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger: Logger = QccLogger.getLogger(GlobalExceptionFilter.name);
  // private readonly sentryHelper: SentryHelper;

  constructor() {
    // this.sentryHelper = SentryHelper.getInstance();
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    // this.sentryHelper.fulfillScope(request);
    const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
    const resData = this.formatException(request, exception);
    // this.sentryHelper.captureHttpException(exception, resData);
    // if (process.env.NODE_ENV === 'prod') {
    //   Object.assign(resData, {
    //     message: []
    //   });
    // }
    if (status >= 500) {
      this.logger.error('responseData', JSON.stringify(resData, null, 1));
    }

    // do not modify the original responseData , then sentry will log the original error
    response.status(status).json(resData);
  }

  formatException(request, exception: unknown) {
    const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
    let resData: any;
    // sample
    // {
    //   "statusCode": 400,
    //   "error": "Bad Request",
    //   "message": [
    //   {
    //     "target": {
    //       "pageSize": 10,
    //       "pageIndex": null
    //     },
    //     "value": null,
    //     "property": "pageIndex",
    //     "children": [],
    //     "constraints": {
    //       "min": "pageIndex must not be less than 1",
    //       "isNumber": "pageIndex must be a number"
    //     }
    //   }
    // ],
    //   "timestamp": "2019-10-31T02:44:32.856Z",
    //   "path": "/kzz/user/messages?pageSize=10&pageIndex=x"
    // }

    // {
    //   "message": [
    //   "error1"
    // ],
    //   "error": "Internal Server Error",
    //   "statusCode": 500,
    //   "timestamp": "2019-10-31T02:40:06.448Z",
    //   "path": "/kzz/user/error1"
    // }
    if (exception instanceof HttpException) {
      resData = exception.getResponse();
      if (resData.message && !Array.isArray(resData.message)) {
        resData.message = [resData.message];
      }
    } else {
      resData = {
        message: ['Internal server error'],
        error: 'Internal Server Error',
        code: status,
      };
      if (exception instanceof Error) {
        resData.message = [exception.message];
      }
      this.logger.error(exception);
    }
    if (status === 400) {
      resData = Object.assign({}, CommonExceptions.BadParams.Common, resData);
    }
    const responseData: SearchExceptionResponse = Object.assign({}, resData, {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    });
    // @ts-ignore
    delete responseData.internalMessage;
    // @ts-ignore
    delete responseData.internalError;
    // @ts-ignore
    delete responseData.errorExtraData;
    return responseData;
  }
}
