import { max, split } from 'lodash';

/**
 * @description 验证字符串是否是有效的KeyNo
 * @param {*} input 待校验参数
 */
const isValidKeyNo = (input: string) => {
  return (
    input &&
    (/^[a-f0-9]{32}$/.test(input) ||
      /^[shgtxwyjozpm][a-f0-9]{31}$/.test(input) ||
      /^pr[a-f0-9]{30}$/.test(input) ||
      /^pr[a-f0-9]{28}x[a-f0-9]{1}$/.test(input) ||
      /^p[a-f0-9]{29}x[a-f0-9]{1}$/.test(input))
  );
};

const isValidCompanyEnglishName = (input: string) => {
  return input && /^[A-Za-z]+[^\u4e00-\u9fa5]*$/.test(input);
};

const isContainsEnglishCharacters = (input: string) => {
  return input && /[A-Za-z]/.test(input);
};

const isValidPinYin = (input: string) => {
  return input && input.replace(/(^\s+)|(\s+$)/g, '').replace(/\s/g, '').length > 3 && /^[A-Za-z\s?]+$/.test(input);
};

/**
 * @description 验证字符串是否是有效的数字
 * @param {*} input 待校验参数
 */
const isNumber = function (input) {
  if (!input) return false; // 增加对空输入的处理
  return /^-?\d{1,11}$/.test(input);
};

// const getMultiConditionAttributes = (input) => {
//   let obj = {};

//   try {
//     obj = JSON.parse(input);
//   } catch (e) {
//     obj = {};
//   }

//   return keys(obj);
// };

const isValidUrl = (input: string) => {
  // updated by chuanliang on 25 Oct 2021
  // return input && /^(?=^.{3,255}$)(http(s)?:\/\/)?(www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)*(\/\w*)*(\/\w+(-\w*)*\.\w+)*([?&]\w+=\w*)*$/.test(input)
  return input && /^(?=^.{3,255}$)(http(s)?:\/\/)?(www\.)?\w[-\w]{0,62}(\.\w[-\w]{0,62})+(:\d+)*(\/(\w+)*(-\w*)*(\.\w+)*)*([?&](\w+=\w*)?)*$/.test(input);
};

// const isValidEmail = (input) => {
//   return input && /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/.test(input);
// };

// const isContainsNumber = (input) => {
//   return input && /(.*)?\d+(.*)?/.test(input);
// };

// const isValidEmailAddress = (input: string) => {
//   const checkList = new Set([
//     'qq',
//     'qqc',
//     'qqco',
//     'qqcom',
//     'sinacom',
//     'sina',
//     'sinacomcn',
//     '163',
//     '163com',
//     '126',
//     '126com',
//     'yahoo',
//     'yahoocom',
//     '21cn',
//     '21cncom',
//     'tom',
//     'tomcom',
//     'google',
//     'googlecom',
//     'hotmail',
//     'hotmailcom',
//     'msn',
//     'msncom',
//     '189',
//     '189com',
//     '139',
//     '139com',
//   ]);

//   return input && !checkList.has(input.toLowerCase().replace('@', '').replace('.', '')) && /^[A-Za-z0-9@.\-_]*$/.test(input);
// };

// const isValidContactNumber = (input: string) => {
//   return input && (/^\d{7,11}$/.test(input) || /^\d{3,4}-\d{7,8}$/.test(input));
// };

// const isValidWebsite = (input) => {
//   const checkList = new Set([
//     'com',
//     '.com',
//     'cn',
//     '.cn',
//     'net',
//     '.net',
//     'com.cn',
//     '.com.cn',
//     'org',
//     '.org',
//     'gov',
//     '.gov',
//     'gov.cn',
//     '.gov.cn',
//     'http',
//     'https',
//     'http:',
//     'http:/',
//     'http://',
//     'https:',
//     'https:/',
//     'https://',
//     'w',
//     'ww',
//     'www',
//     'www.',
//   ]);

//   return input && !checkList.has(input.toLowerCase()) && /[a-zA-Z]/.test(input);
// };

const isContainsEm = (input) => {
  return input && input.indexOf('<em>') > -1 && input.indexOf('</em>') > -1;
};

const removeEm = (input) => {
  let output = input;
  if (isContainsEm(input)) {
    output = input.replace(/<em>/g, '').replace(/<\/em>/g, '');
  }
  return output;
};

const getEmCharCnt = (input) => {
  const source = input;
  if (!source || !isContainsEm(source)) return 0;
  let result = 0;
  let temp = source;

  while (isContainsEm(temp)) {
    const startEm = temp.indexOf('<em>');
    const endEm = temp.indexOf('</em>');
    result += endEm - startEm - 4;
    temp = temp.substr(endEm + 5);
  }
  return result;
};

const getCharCntInsideEm = (input) => {
  const source = input;
  if (source && source.indexOf(',') > 0) {
    const list = split(source, ',').map(function (item) {
      return getEmCharCnt(item);
    });
    return max(list);
  } else {
    return getEmCharCnt(source);
  }
};

// const isContainsWords = function (input, checkedArray) {
//   const source = input;
//   if (!source || checkedArray.length === 0) {
//     return false;
//   }
//
//   const filteredData = filter(checkedArray, (p) => source.indexOf(p) > -1);
//
//   return filteredData && filteredData.length;
// };

const isContainsWords = function (input, checkedArray) {
  if (!input || checkedArray.length === 0) {
    return false;
  }
  try {
    return checkedArray.some((p) => input.includes(p));
  } catch (e) {
    console.error('Error in isContainsWords:', e);
    return false;
  }
};

// /*
// 转全角字符
//  */
// const toSBC = function (input) {
//   let result = '';
//   for (let i = 0; i < input.length; i++) {
//     let cCode = input.charCodeAt(i);
//     // 全角与半角相差（除空格外）：65248(十进制)
//     cCode = cCode >= 0x0021 && cCode <= 0x007e ? cCode + 65248 : cCode;
//     // 处理空格
//     cCode = cCode === 0x0020 ? 0x03000 : cCode;
//     result += String.fromCharCode(cCode);
//   }
//   return result;
// };

// /*
// 转半角字符
//  */
// const toDBC = function (input) {
//   let result = '';
//   for (let i = 0; i < input.length; i++) {
//     let cCode = input.charCodeAt(i);
//     // 全角与半角相差（除空格外）：65248（十进制）
//     cCode = cCode >= 0xff01 && cCode <= 0xff5e ? cCode - 65248 : cCode;
//     // 处理空格
//     cCode = cCode === 0x03000 ? 0x0020 : cCode;
//     result += String.fromCharCode(cCode);
//   }
//   return result;
// };

// const isSameDBC = function (from, to) {
//   return upperCase(toDBC(from).replace(/\s+/g, '')) === upperCase(toDBC(to).replace(/\s+/g, ''));
// };

const getValidCompanyName = (input) => {
  if (!input) return '';

  if (input === '北京奇虎360科技有限公司') return input;

  const temp = input.replace(/[ °！!@#$%^&*＊.,。，;；？?※]/g, '');

  if (temp.length < 4) return '';

  const regexp = /(.*[\u4e00-\u9fa5]+[^-－A-Za-z])/i;
  const res = regexp.exec(temp);
  if (res && res.length > 1) {
    return res[1];
  } else {
    return null;
  }
};

// const isValidUrlV2 = (input) => {
//   return (
//     input &&
//     /^(?=^.{3,255}$)(http(s)?:\/\/)?(www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)*(\/\w*)*(\/\w+(-\w*)*\.\w+)*([?&](\w+=\w*)?)*$/.test(
//       input,
//     )
//   );
// };

const formatWebSite = (input: string): string => {
  if (!input) return '';

  let source = input;
  source = source.toLowerCase().replace('http://', '').replace('https://', '');
  return source.split('/')[0];
};

// const getDomain = (input: string): string => {
//   if (!input) return '';
//   return formatWebSite(input).replace('www.', '');
// };

const formatContactNumber = (input: string): string => {
  if (!input) return '';

  const source = input;
  if (source.startsWith('+86')) {
    return source.replace('+86', '');
  } else if (source.startsWith('86')) {
    return source.replace('86', '');
  } else {
    return source;
  }
};

const getChinese = (input: string): string[] => {
  if (input) {
    const result = input.match(/[\u4e00-\u9fa5]/g);
    if (result?.length) return result;
  }
  return [];
};

export const stringUtils = {
  isValidKeyNo,
  isNumber,
  // getMultiConditionAttributes,
  isValidUrl,
  // isValidEmail,
  // isContainsNumber,
  // isValidEmailAddress,
  // isValidContactNumber,
  // isValidWebsite,
  isContainsEm,
  removeEm,
  getCharCntInsideEm,
  isContainsWords,
  // toSBC,
  // toDBC,
  // isSameDBC,
  getValidCompanyName,
  // isValidUrlV2,
  isValidCompanyEnglishName,
  isValidPinYin,
  isContainsEnglishCharacters,
  formatWebSite,
  formatContactNumber,
  // getDomain,
  getChinese,
};
