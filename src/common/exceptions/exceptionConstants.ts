export const CommonExceptions = {
  TypeORM: {
    QueryFailed: {
      code: 600001,
      error: '数据库查询错误',
    },
  },
  BadParams: {
    Common: {
      code: 700001,
      error: '参数错误',
    },
  },
  // 80xxxx
  ES: {
    // 8001xx
    Aggs: {
      Terms: {
        code: 800101,
        error: 'terms 聚合错误',
      },
      Range: {
        code: 800102,
        error: 'range 聚合错误',
      },
      Histogram: {
        code: 800101,
        error: 'histogram 聚合错误',
      },
    },
    // 8002xx
    Server: {
      Error: {
        code: 800201,
        error: 'ES server 错误',
      }, //
      OverLimit: {
        code: 800202,
        error: 'ES server 访问超频',
      }, //
    },
    // 8003xx
    Company: {
      IdRequried: {
        code: 800301,
        error: '缺少companyId',
      }, //
    },
    // 8004xx
    Result: {
      MappingError: {
        code: 800400,
        error: '结果处理异常',
      }, //
    },
  },
  Common: {
    // 9001xx
    Request: {
      AccessDenied: {
        code: 900100,
        error: '访问被拒绝',
      },
      ReachLimit: {
        code: 900101,
        error: '请求数达到最大限制',
      },
      Duplicated: {
        code: 900102,
        error: '记录已存在，请勿重复提交',
      },
      NotFound: {
        code: 900103,
        error: '记录不存在',
      },
      BatchLimited: {
        code: 900104,
        error: '批量操作数量超过上限',
      },
      AccessCheckError: {
        code: 900105,
        error: '服务错误(accesscheck)',
      },
    },
    // 9002xx
    SMS: {
      Error: {
        code: 900200,
        error: '短信发送失败',
      },
      Duplicated: {
        code: 900201,
        error: '60秒内只允许发送一次验证码',
      },
    },
    //9003xx
    Export: {
      NotFound: {
        code: 900301,
        error: '导出记录不存在',
      },
      RetryDenied: {
        code: 900302,
        error: '导出操作正在执行或已成功',
      },
    },
    //9004xx
    AliGreen: {
      Error: {
        code: 900400,
        error: '请求阿里绿网检测敏感词失败',
      },
      Block: {
        code: 900401,
        error: '关键词为敏感词汇',
      },
    },
    //9005xx
    QccService: {
      Error: {
        code: 900500,
        error: '请求内部服务失败',
      },
      InvalidWeixinToken: {
        code: 900501,
        error: '获取weixin token失败',
      },
      InvalidToken: {
        code: 900502,
        error: '获取token失败',
      },
    },
    //9006xx
    WeixinService: {
      Error: {
        code: 900600,
        error: '请求微信服务失败',
      },
    },
    PushApiCallback: {
      ConnectFailed: {
        code: 900700,
        error: '接口无法访问',
      },
      ResponseError: {
        code: 900701,
        error: '接口响应异常',
      },
      Timeout: {
        code: 900702,
        error: '接口响应超时',
      },
    },
  },
};
