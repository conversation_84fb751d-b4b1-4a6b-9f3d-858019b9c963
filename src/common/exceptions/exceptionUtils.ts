import { BadRequestException, HttpStatus, ValidationError } from '@nestjs/common';
import { CommonExceptions } from './exceptionConstants';

/**
 * class-validator 验证发现错误之后，调用该方法生成 HTTPException
 * @param messages
 */
export const handClassValidatorError = (messages: ValidationError[]) => {
  const handle = (errorMsg, parentProperties, collector) => {
    const properties = parentProperties.concat([]);
    if (errorMsg.children && errorMsg.children.length) {
      properties.push(errorMsg.property);
      return errorMsg.children.map((c) => handle(c, properties, collector));
    }
    const constraints: string[] = Object.values(errorMsg.constraints);
    const currentProperty = errorMsg.property;

    const o = {
      property: properties.concat([currentProperty]).join('.'),
      constraints: constraints.map((t) => t.replace(currentProperty, '')),
    };
    collector.push(o);
  };

  const results = messages.reduce((prev, curren) => {
    handle(curren, [], prev);
    return prev;
  }, []);
  return new BadRequestException(
    Object.assign(
      {
        statusCode: HttpStatus.BAD_REQUEST,
        message: results,
      },
      CommonExceptions.BadParams.Common,
    ),
  );
};

// export const formatHttpException = (request, exception: unknown, logger?: Logger) => {
//   const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
//   let resData: any;
//   // sample
//   // {
//   //   "statusCode": 400,
//   //   "error": "Bad Request",
//   //   "message": [
//   //   {
//   //     "target": {
//   //       "pageSize": 10,
//   //       "pageIndex": null
//   //     },
//   //     "value": null,
//   //     "property": "pageIndex",
//   //     "children": [],
//   //     "constraints": {
//   //       "min": "pageIndex must not be less than 1",
//   //       "isNumber": "pageIndex must be a number"
//   //     }
//   //   }
//   // ],
//   //   "timestamp": "2019-10-31T02:44:32.856Z",
//   //   "path": "/kzz/user/messages?pageSize=10&pageIndex=x"
//   // }

//   // {
//   //   "message": [
//   //   "error1"
//   // ],
//   //   "error": "Internal Server Error",
//   //   "statusCode": 500,
//   //   "timestamp": "2019-10-31T02:40:06.448Z",
//   //   "path": "/kzz/user/error1"
//   // }
//   if (exception instanceof HttpException) {
//     resData = exception.getResponse();
//     if (resData.message && !Array.isArray(resData.message)) {
//       resData.message = [resData.message];
//     }
//   } else {
//     resData = {
//       message: ['Internal server error'],
//       error: 'Internal Server Error',
//       code: status
//     };
//     if (exception instanceof Error) {
//       resData.message = [exception.message];
//     }
//     if (logger) {
//       logger.error(exception);
//     }
//   }
//   if (status === 400) {
//     resData = Object.assign({}, CommonExceptions.BadParams.Common, resData);
//   }
//   const responseData: SearchExceptionResponse = Object.assign(resData, {
//     statusCode: status,
//     timestamp: new Date().toISOString(),
//     path: request.url,
//     method: request.method
//   });
//   // @ts-ignore
//   delete responseData.internalMessage;
//   // @ts-ignore
//   delete responseData.internalError;
//   // @ts-ignore
//   delete responseData.errorExtraData;
//   return responseData;
// };
