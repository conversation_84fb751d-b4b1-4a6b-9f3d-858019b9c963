/**
 * 汉字转拼音
 * 支持完整拼音和拼音首字母
 */

// 带声调的字典文件
import { PinyinDictWithTone } from './dict/PinyinDictWithTone';

// 存储所有字典数据
const dict = {
  withtone: {},
};
const temp = PinyinDictWithTone.split(',');
for (let i = 0, len = temp.length; i < len; i++) {
  // 这段代码耗时28毫秒左右，对性能影响不大，所以一次性处理完毕
  dict.withtone[String.fromCharCode(i + 19968)] = temp[i]; // 这里先不进行split(' ')，因为一次性循环2万次split比较消耗性能
}

/**
 * 去除拼音中的声调，比如将 xiǎo míng tóng xué 转换成 xiao ming tong xue
 * @param pinyin 需要转换的拼音
 */
const removeTone = (pinyin) => {
  const toneMap = {
    ā: 'a1',
    á: 'a2',
    ǎ: 'a3',
    à: 'a4',
    ō: 'o1',
    ó: 'o2',
    ǒ: 'o3',
    ò: 'o4',
    ē: 'e1',
    é: 'e2',
    ě: 'e3',
    è: 'e4',
    ī: 'i1',
    í: 'i2',
    ǐ: 'i3',
    ì: 'i4',
    ū: 'u1',
    ú: 'u2',
    ǔ: 'u3',
    ù: 'u4',
    ü: 'v0',
    ǖ: 'v1',
    ǘ: 'v2',
    ǚ: 'v3',
    ǜ: 'v4',
    ń: 'n2',
    ň: 'n3',
    '': 'm2',
  };
  return pinyin.replace(/[āáǎàōóǒòēéěèīíǐìūúǔùüǖǘǚǜńň]/g, function (m) {
    return toneMap[m][0];
  });
};

/**
 * 根据汉字获取拼音，如果不是汉字直接返回原字符
 * @method
 *
 * @param  {String}   options.chinese   要转换的汉字
 * @param  {String}   options.splitter  分隔字符，默认用空格分隔
 * @param  {Boolean}  options.polyphone 是否支持多音字，默认是
 * @return {Object}
 *
 * <AUTHOR> Zhang <<EMAIL>>
 * @since  2017-06-29T14:55:37+0800
 * @version 1.0
 */
export const getPinYin = ({ chinese, splitter = ' ', polyphone = true, withtone = false }) => {
  const result = {
    pinyin: '',
    firstletter: '',
  };
  const notonePinYin = [];
  const firstLetterPinYin = [];

  if (!chinese || /^ +$/g.test(chinese)) return result;

  for (let i = 0; i < chinese.length; i++) {
    const tmp = chinese.charAt(i);
    let tmpPinYin = dict.withtone[tmp] || tmp;
    // 去除声调
    if (withtone !== true) {
      tmpPinYin = removeTone(tmpPinYin);
    }
    // 暂时不支持多音字
    tmpPinYin = tmpPinYin.replace(/ .*$/g, '');
    notonePinYin.push(tmpPinYin);
    firstLetterPinYin.push(tmpPinYin.substr(0, 1));
  }

  if (polyphone) {
    // TODO... 处理多音字
  }
  result.pinyin = notonePinYin.join(splitter);
  result.firstletter = firstLetterPinYin.join(splitter);

  return result;
};

// console.log(getPinYin({chinese: '张全排', withtone: true}))
