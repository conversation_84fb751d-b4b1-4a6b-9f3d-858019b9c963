import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from '../app/app.module';
import { CompanySearchService } from './company.service';
import { ExactMatchCompanyRequest, KysCompanySearchRequest } from './model';
import { ESTotal } from '@kezhaozhao/qcc-model';

jest.setTimeout(30000);
describe('CompanySearchService test', () => {
  let companySearch: CompanySearchService;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    companySearch = module.get(CompanySearchService);
  });

  it('获取单个企业详情', async () => {
    const { result } = await companySearch.findByKys('f625a5b661058ba5082ca508f99ffe1b');
    expect(result.creditcode).toEqual('91320594088140947F');
  });

  it('工商查询-searchIndex=name', async () => {
    const request = Object.assign(new KysCompanySearchRequest(), {
      searchKey: '企查查',
      filter: { r: [{ pr: 'JS', ac: 3205 }] },
      searchIndex: ['name'],
      isHighlight: true,
      pageIndex: 1,
      pageSize: 10,
    });

    const res = await companySearch.kysSearch(request);
    expect((res.Paging.TotalRecords as ESTotal).value).toBeGreaterThan(0);
  });

  it('工商查询-searchIndex全部', async () => {
    const request = Object.assign(new KysCompanySearchRequest(), {
      searchKey: '乐视',
      // filter: { r: [{ pr: 'JS', ac: 3205 }] },
      searchIndex: ['name', 'product', 'scope', 'patent', 'website', 'address'],
      isHighlight: true,
      pageIndex: 1,
      pageSize: 10,
    });

    const res = await companySearch.kysSearch(request);
    expect((res.Paging.TotalRecords as ESTotal).value).toBeGreaterThan(0);
  });

  it('获取工商联系方式', async () => {
    const { tellist, emaillist } = await companySearch.getContactByKys('f625a5b661058ba5082ca508f99ffe1b');
    expect(tellist.length).toBeGreaterThan(0);
    expect(emaillist.length).toBeGreaterThan(0);
  });

  it('匹配公司完整企业名称/统一社会信用代码/注册号', async () => {
    const request = Object.assign(new ExactMatchCompanyRequest(), {
      searchKey: ['江苏盐阜公路运输集团有限公司', '江苏盐阜公路运输集团有限公司', '91320594088140947F', '91320594MA27GDUP5T'],
      searchFields: ['name', 'creditcode', 'regno'],
      includeFields: ['id', 'name', 'creditcode', 'regno', 't_type'],
      searchType: undefined,
    });
    const data = await companySearch.kysExactMatchSearch(request);
    expect(data.length).toBeGreaterThan(0);
  });
});
