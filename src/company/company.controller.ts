import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ExactMatchCompanyRequest, Contact, KysCompanyResponseDetails, KysCompanySearchRequest } from './model';
import { CompanySearchService } from './company.service';
import { ESDetailResopne, ESResponse } from '@kezhaozhao/qcc-model';

@Controller('')
@ApiTags('Company')
export class CompanyController {
  constructor(private readonly companySearchService: CompanySearchService) {}

  @Post('kys/search')
  @ApiOperation({
    description: 'search company  from kys index',
    summary: '查询kys索引获取公司信息',
  })
  @ApiOkResponse({ type: KysCompanyResponseDetails })
  public async kysSearch(@Body() searchRequest: KysCompanySearchRequest): Promise<ESResponse<KysCompanyResponseDetails>> {
    return this.companySearchService.kysSearch(searchRequest);
  }

  @Post('kys/contact/:companyId')
  @ApiOperation({
    description: '获取企业的所有联系方式',
    summary: '获取企业的所有联系方式',
  })
  @ApiOkResponse({ type: Contact })
  public async kysSearchContact(@Param('companyId') companyId: string): Promise<Contact> {
    return this.companySearchService.getContactByKys(companyId);
  }

  @Post('kys/search/exact')
  @ApiOperation({
    description: '匹配公司完整企业名称/统一社会信用代码/注册号',
    summary: '按关键字完整匹配',
  })
  @ApiOkResponse({ type: KysCompanyResponseDetails })
  public async matchExactlyForKys(@Body() searchRequest: ExactMatchCompanyRequest): Promise<KysCompanyResponseDetails[]> {
    return this.companySearchService.kysExactMatchSearch(searchRequest);
  }

  @Get('kys/:companyId')
  @ApiOperation({
    description: 'company details v2 from kzz es',
    summary: 'v2版 企业详情',
  })
  @ApiOkResponse({ type: KysCompanyResponseDetails })
  public async kysDetails(@Param('companyId') companyId: string): Promise<ESDetailResopne<KysCompanyResponseDetails>> {
    return await this.companySearchService.findByKys(companyId);
  }
}
