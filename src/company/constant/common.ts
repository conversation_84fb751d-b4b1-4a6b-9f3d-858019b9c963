/**
 * 筛选条件参数
 */
export const ConditionParams = {
  // 号码 status 1 可用， 2 不可用  3 未知
  // 原始状态 0：空号, 1：实号, 2：停机, 3：库无, 4：沉默号, 5：风险号
  telStatus: {
    1: '可用', // 可用: 1:实号
    2: '不可用', // 不可用：0: 空号、2:停机、4:沉默号、5:风险号
    3: '未知', // 未知：3:库无 或其他未罗列出来的状态
  },
  //  经营状态 statuscode
  statusCode: {
    10: '在业', // 正常
    20: '存续',
    30: '筹建',
    40: '清算',
    50: '迁入',
    60: '迁出',
    70: '停业',
    75: '歇业',
    85: '责令关闭',
    80: '撤销',
    90: '吊销',
    99: '注销',
    93: '其他',
    92: '仍注册',
    94: '已告解散',
    95: '已终止营业地点',
    96: '不再是独立的实体',
    97: '休止活动',
    100: '废止',
    101: '废止清算完结',
    102: '废止许可',
    103: '废止许可完结',
    104: '废止认许',
    105: '废止认许完结',
    106: '接管',
    107: '撤回认许',
    108: '撤回认许完结',
    110: '撤销设立',
    111: '撤销完结',
    112: '撤销无需清算',
    113: '撤销许可',
    114: '撤销认许',
    115: '撤销认许完结',
    116: '核准报备',
    117: '核准设立',
    118: '设立但已解散',
    119: '核准许可报备',
    120: '核准许可登记',
    121: '核准认许',
    122: '清理',
    123: '清理完结',
    124: '破产',
    125: '破产清算完结',
    126: '破产程序终结',
    127: '解散',
    128: '解散清算完结',
    129: '重整',
    130: '合并解散',
    131: '终止破产',
    132: '涂销破产',
    133: '核准许可',
    134: '核准登记',
    135: '分割解散',
    136: '废止登记完结',
    137: '废止登记',
    138: '撤销登记完结',
    139: '撤销登记',
    140: '撤回登记完结',
    141: '撤回登记',
  },
  //  资本类型 registerunit
  registCapitalUnit: {
    CNY: '人民币',
    TWD: '新台币',
    USD: '美元',
    HKD: '港元',
    EUR: '欧元',
    JPY: '日元',
    OTHER: '其他',
  },
  //  企业类型 econkindcode,
  econKindCode: {
    10: '有限责任公司',
    20: '股份有限公司',
    30: '国企',
    40: '外商投资企业',
    50: '独资企业',
    70: '个体工商户',
    80: '联营企业',
    90: '集体所有制',
    100: '有限合伙',
    110: '普通合伙',
  },
  // 组织机构 type
  orgType: {
    0: '大陆企业',
    1: '社会组织',
    3: '香港企业',
    4: '除基金会、事业单位、律师事务所之外的',
    5: '台湾公司',
    10: '基金会',
    11: '事业单位',
    12: '律师事务所',
    21: '美股企业',
  },
  //  融资阶段 financinglevel
  // 0-无融资信息、
  financeStageType: {
    1: '天使/种子轮',
    2: 'Pre-A至A+轮',
    3: 'Pre-B至B+轮',
    4: 'C轮及以上',
    5: '新三板/上市',
    6: '收购/并购/被并购',
    7: '战略投资',
    8: '其他',
  },
  // listingstatus 上市板块
  listingStatusInfo: {
    '-1': '未上市',
    1: '新三板',
    // 2: 'A股', 已拆分成  21   主板   22   中小板  23  创业板
    21: '主板',
    22: '中小板',
    23: '创业板',
    6: '港股',
    7: '中概股',
    301: '新四板',
    501: '科创板',
  },
  companyChangeCategory: [
    '名称变更',
    '股东变更',
    '法人变更',
    '投资人变更',
    '负债人变更',
    '经营者变更',
    '股权/转让变更',
    '经营场所变更',
    '经营范围变更',
    '经营期限变更',
    '联系方式变更',
    '企业类型变更',
    '注册资本变更',
    '其他',
  ],
  //  企业潜力 flag
  qyql: {
    HT001: '高新技术企业',
    UE: '独角兽企业',
    GE: '瞪羚企业',
    YEE: '雏鹰企业',
    ITE: '创新型科技企业',
    STE: '科技小巨人企业',
    SSE: '专精特新企业',
    T_TSMES: '科技型中小企业',
    T_PT: '民营科技企业',
    T_TC: '企业技术中心',
    T_MS: '国家备案众创空间',
    T_CI: '科技企业孵化器',
    T_TD: '技术创新示范企业',
    T_IC: '隐形冠军企业',
    T_ATS: '技术先进型服务企业',
    T_AC: '牛羚企业',
    XWQY: '小微企业',
  },
  flag: {
    T: '有固话',
    MN: '有手机',
    E: '有邮箱',
    YR: '有无年报',
    B: '有无分支机构',
    GT: '是否一般纳税人',
    // NT: '纳税人资格类型(A级/非A级)',
    CR: '有无变更信息',
    RE: '有无招聘信息',
    IM: '有无控股企业',
    FIV: '有无投资机构',
    SMV: '有无私募基金',
    TF: '是否500强企业',
    TE: '有无招投标',
    NJX: '有无拟建项目',
    CI: '有无进出口信用',
    F: '有无融资信息',
    K: '是否上市企业',
    LP: '有无地块公示',
    BL: '有无购地信息',
    LT: '有无土地转让',
    IVM: '有无对外投资',
    BM: '有无建筑资质',
    XZXK: '有无行政许可',
    EP: '有无经营异常',
    YZWF: '有无严重违法',
    GQZY: '有无股权质押',
    AOP: '有无行政处罚',
    ENP: '有无环保处罚',
    TAB: '有无税收违法',
    SFPW: '有无司法拍卖',
    XJPG: '有无询价评估',
    PCCZ: '有无破产重整',
    TDDY: '有无土地抵押',
    GSCG: '有无公示催告',
    QSGG: '有无欠税公告',
    ZX: '有无被执行人信息',
    S: '有无失信被执行人',
    EC: '有无终本案件',
    XG: '有无限制高消费',
    CPWS: '有无裁判文书',
    FYGG: '有无法院公告',
    KTGG: '有无开庭公告',
    GQDJ: '有无股权冻结/有无司法协助',
    LA: '有无立案信息',
    ZS: '有无证书',
    C: '有无作品著作权',
    SC: '有无软件著作权',
    TELB: '有无ICP备案',
    APP: '有无APP',
    WP: '有无小程序',
    WB: '有无微博',
    GZH: '有无微信公众号',
    LIC: '有电信许可',
    GW: '有无官网',
    P: '有专利',
    M: '有商标',
    MP: '有无动产抵押',
    PL: '有股权出质',
    UE: '独角兽企业',
    GE: '瞪羚企业',
    YEE: '雏鹰企业',
    ITE: '创新型科技企业',
    STE: '科技小巨人企业',
    SSE: '专精特新企业',
    T_TSMES: '科技型中小企业',
    T_PT: '民营科技企业',
    T_TC: '企业技术中心',
    T_MS: '国家备案众创空间',
    T_CI: '科技企业孵化器',
    T_TD: '技术创新示范企业',
    T_IC: '隐形冠军企业',
    T_ATS: '技术先进型服务企业',
    T_AC: '牛羚企业',
    XWQY: '小微企业',
    HT001: '高新技术企业',
    TZJG: '有无投资机构',
    SFAJ: '有无司法案件',
    CQJY: '有无产权交易',
    XYPJ: '有无信用评级',
    JYZX: '简易注销',
    KZZ_IPS: '有无知识产权出质',
    KZZ_YQ: '是否央企',
    KZZ_NO_NTM: '无失信人',
    KZZ_PART_NTM: '部分失信',
    KZZ_ALL_NTM: '全部失信',
    KZZ_NO_EM: ' 无可执行',
    KZZ_PART_EM: '部分可执行',
    KZZ_ALL_EM: '全部可执行',
    KZZ_NO_LH: '无限高',
    KZZ_PART_LH: '部分限高',
    KZZ_ALL_LH: '全部限高',
    KZZ_OPC: '有无国际专利',
    KZZ_APP_NOT_REGIST: 'app未注册商标',
    KZZ_IS_AGENCY: '是代理机构',
    KZZ_PROP_SHIP: '是独资企业',
    KZZ_CFC: '商业特许经营',
    KZZ_PATENT_NAME_CHANGE: '专利关联主体名称变更',
    KZZ_SOFT_NAME_CHANGE: '软著关联主体名称变更',
    KZZ_HAVE_IPRIGHT_JUDG: '是否有商标文书',
    SXFFS: '涉嫌非法社会组织',
    YQDFFS: '已取缔非法社会组织',
    FNC_JR: '金融机构',
    FNC_BX: '保险机构',
    FNC_ZJ: '保险中介机构',
  },
  contract: {
    T: '有无固话',
    MN: '有无手机',
    E: '有无邮箱',
  },
  tenderIndustry: {
    1: '建筑工程',
    2: '行政办公',
    3: '医疗卫生',
    4: '服务采购',
    5: '机械设备',
    6: '水利水电',
    7: '能源化工',
    8: '弱电安防',
    9: '信息技术',
    10: '交通工程',
    11: '市政设施',
    12: '农林牧渔',
    13: '政府采购',
    14: '药品采购',
    15: '其他',
  },
  tenderIfbprogress: {
    1: '拟建项目',
    3: '招标公告',
    4: '中标结果',
    5: '其他',
    31: '招标',
    32: '邀标',
    33: '竞谈',
    34: '询价',
    35: '变更',
    36: '竞价',
    37: '其他',
    38: '预告',
    39: '竞磋',
    41: '开标',
    42: '中标',
    43: '成交',
    44: '变更',
    45: '终止',
    46: '废标',
    47: '流标',
    49: '其他',
    10: '其他',
    12: '审批未通过',
    13: '审批通过',
    14: '审批中',
    15: '未审批',
    16: '撤销',
    48: '合同及验收',
    310: '单一来源',
    311: '澄清答疑',
    312: '资审',
  },
  patenStatus: {
    '01': '有效',
    '02': '失效',
    '03': '审中',
    '04': '未确认',
    '0101': '授权',
    '0102': '权利恢复',
    '0103': '部分无效',
    '0201': '全部无效',
    '0202': '驳回',
    '0203': '权利终止',
    '0204': '撤回',
    '0205': '避重授权',
    '0206': '未缴年费',
    '0207': '期限届满',
    '0208': '放弃',
    '0209': '撤销',
    '0301': '公开',
    '0302': '实质审查',
    '0401': '未确认',
  },
  // 经营异常原因类型整理
  exceptionType: {
    1: '登记的住所/经营场所无法联系企业',
    2: '未按规定公示企业信息',
    3: '公示信息隐瞒真实情况/弄虚作假',
    4: '未在登记所从事经营活动',
    5: '未在规定期限公示年度报告',
    6: '商事主体名称不适宜',
    7: '其他',
  },
  //  营收水平万元  利润水平万元  资产规模  对应等级 对应 金额 （万元）
  businessScaleCode: {
    // 对应等级:  金额 （万元）
    N1: 'N<=-100',
    N2: '-100<N<=-50',
    N3: '-50<N<=-30',
    N4: '-30<N<=-20',
    N5: '-20<N<=-10',
    N6: '-20<N<=-10',
    N7: '-10<N<0',
    P0: 'P=0',
    P1: '0<P<=1',
    P2: '1<P<=5',
    P3: '5<P<=10',
    P4: '10<P<=20',
    P5: '20<P<=30',
    P6: '30<P<=50',
    P7: '50<P<=100',
    P8: '100<P<=150',
    P9: '150<P<=200',
    P10: '200<P<=300',
    P11: '300<P<=500',
    P12: '500<P<=1000',
    P13: '1000<P<=2000',
    P14: '2000<P<=5000',
    P15: '5000<P<=10000',
    P16: '10000<P<=20000',
    P17: '20000<P<=50000',
    P18: '50000<P<=100000',
    P19: '100000<P<=500000',
    P20: '500000<P<=1000000',
    P21: '1000000<P<=2000000',
    P22: '2000000<P<=5000000',
    P23: '5000000<P<=10000000',
    P24: '10000000<P',
  },
};

/**
 * 证书类型
 */
export const CretCode = [
  {
    label: '安全生产许可证',
    value: '1501',
  },
  {
    label: '建筑资质',
    value: '03',
    children: [
      {
        label: '工程勘察',
        value: '0301',
        children: [
          { label: '工程勘察工程测量专业丙级', value: '030101' },
          { label: '工程勘察水文地质勘察专业丙级', value: '030102' },
          { label: '工程勘察资质证书', value: '030103' },
        ],
      },
      {
        label: '工程招标代理',
        value: '0302',
        children: [
          { label: '工程招标代理乙级', value: '030201' },
          { label: '工程招标代理暂定级', value: '030202' },
          { label: '工程招标代理甲级', value: '030203' },
          { label: '招标代理机构资质证书', value: '030204' },
          { label: '招标代理资格', value: '0302005' },
          { label: '招标代理资质证书', value: '030206' },
        ],
      },
      {
        label: '工程监理',
        value: '0303',
        children: [
          { label: '信息系统工程监理资质单位证书', value: '030301' },
          { label: '信息系统工程监理资质工程师证书', value: '030302' },
          { label: '工程监理企业资质证书', value: '030303' },
          { label: '工程监理市政公用工程专业乙级', value: '030304' },
          { label: '工程监理房屋建筑工程专业丙级', value: '030305' },
          { label: '工程监理房屋建筑工程专业乙级', value: '030306' },
          { label: '工程监理房屋建筑工程专业甲级', value: '030307' },
          { label: '工程监理机电安装工程专业乙级', value: '030308' },
          { label: '工程监理水利水电工程专业乙级', value: '030309' },
          { label: '工程监理港口与航道工程专业乙级', value: '030310' },
          { label: '工程监理资质证书', value: '030311' },
        ],
      },
      {
        label: '工程设计',
        value: '0304',
        children: [
          { label: '工程设计公路行业公路专业丙级', value: '030401' },
          { label: '工程设计市政行业排水工程专业乙级', value: '030402' },
          { label: '工程设计市政行业桥梁工程专业乙级', value: '030403' },
          { label: '工程设计市政行业给水工程专业乙级', value: '030404' },
          { label: '工程设计市政行业道路工程专业丙级', value: '030405' },
          { label: '工程设计市政行业道路工程专业乙级', value: '030406' },
          { label: '工程设计建筑幕墙工程专项乙级', value: '030407' },
          { label: '工程设计建筑智能化系统专项乙级', value: '030408' },
          { label: '工程设计建筑智能化系统专项甲级', value: '030409' },
          { label: '工程设计建筑行业（人防工程）乙级', value: '030410' },
          { label: '工程设计建筑行业（建筑工程）丁级', value: '030411' },
          { label: '工程设计建筑行业（建筑工程）丙级', value: '030412' },
          { label: '工程设计建筑行业（建筑工程）乙级', value: '030413' },
          { label: '工程设计建筑装饰工程专项丙级', value: '030414' },
          { label: '工程设计建筑装饰工程专项乙级', value: '030415' },
          { label: '工程设计建筑装饰工程专项甲级', value: '030416' },
          { label: '工程设计水利行业灌溉排涝专业丙级', value: '030417' },
          { label: '工程设计消防设施工程专项乙级', value: '030418' },
          { label: '工程设计消防设施工程专项甲级', value: '030419' },
          { label: '工程设计照明工程专项乙级', value: '030420' },
          {
            label: '工程设计环境工程专项（固体废物处理处置工程）乙级',
            value: '030421',
          },
          { label: '工程设计电力行业变电工程专业丙级', value: '030422' },
          {
            label: '工程设计电力行业新能源发电专业乙级',
            value: '030423',
          },
          {
            label: '工程设计电力行业水力发电（含抽水蓄能、潮汐）专业乙级',
            value: '030424',
          },
          { label: '工程设计电力行业甲级', value: '030425' },
          { label: '工程设计资质证书', value: '030426' },
          { label: '工程设计风景园林工程专项乙级', value: '030427' },
          { label: '建筑幕墙工程设计与施工二级', value: '030428' },
        ],
      },
      {
        label: '工程质量检测',
        value: '0305',
        children: [
          {
            label: '建设工程质量检测单位',
            value: '030501',
          },
        ],
      },
      {
        label: '工程造价咨询',
        value: '0306',
        children: [
          { label: '工程造价咨询企业资质证书', value: '030601' },
          { label: '工程造价咨询甲级', value: '030602' },
        ],
      },
      {
        label: '建筑业企业资质',
        value: '0307',
        children: [
          { label: '公路工程施工总承包三级', value: '030701' },
          { label: '公路工程施工总承包二级', value: '030702' },
          { label: '公路路基工程专业承包二级', value: '030703' },
          { label: '古建筑工程专业承包三级', value: '030704' },
          { label: '地基基础工程专业承包一级', value: '030705' },
          { label: '地基基础工程专业承包三级', value: '030706' },
          { label: '地基基础工程专业承包二级', value: '030707' },
          { label: '城市及道路照明工程专业承包一级', value: '030708' },
          { label: '城市及道路照明工程专业承包三级', value: '030709' },
          { label: '市政公用工程施工总承包一级', value: '030710' },
          { label: '市政公用工程施工总承包三级', value: '030711' },
          { label: '市政公用工程施工总承包二级', value: '030712' },
          { label: '建筑业企业资质', value: '030713' },
          { label: '建筑工程施工总承包一级', value: '030714' },
          { label: '建筑工程施工总承包三级', value: '030715' },
          { label: '建筑工程施工总承包二级', value: '030716' },
          { label: '建筑幕墙工程专业承包二级', value: '030717' },
          { label: '建筑机电安装工程专业承包三级', value: '030718' },
          { label: '建筑装修装饰工程专业承包一级', value: '030719' },
          { label: '建筑装修装饰工程专业承包二级', value: '030720' },
          { label: '机电工程施工总承包三级', value: '030721' },
          { label: '机电工程施工总承包二级', value: '030722' },
          { label: '模板脚手架专业承包不分等级', value: '030723' },
          { label: '水利水电工程施工总承包三级', value: '030724' },
          { label: '水利水电工程施工总承包二级', value: '030725' },
          { label: '河湖整治工程专业承包三级', value: '030726' },
          { label: '消防设施工程专业承包一级', value: '030727' },
          { label: '消防设施工程专业承包二级', value: '030728' },
          {
            label: '特种工程(建筑物纠偏和平移)专业承包不分等级',
            value: '030729',
          },
          { label: '特种工程(结构补强)专业承包不分等级', value: '030730' },
          { label: '环保工程专业承包一级', value: '030731' },
          { label: '环保工程专业承包三级', value: '030732' },
          { label: '环保工程专业承包二级', value: '030733' },
          { label: '电力工程施工总承包三级', value: '030734' },
          { label: '电子与智能化工程专业承包二级', value: '030735' },
          { label: '石油化工工程施工总承包一级', value: '030736' },
          { label: '石油化工工程施工总承包三级', value: '030737' },
          { label: '矿山工程施工总承包三级', value: '030738' },
          { label: '起重设备安装工程专业承包三级', value: '030739' },
          { label: '输变电工程专业承包一级', value: '030740' },
          { label: '输变电工程专业承包三级', value: '030741' },
          { label: '钢结构工程专业承包三级', value: '030742' },
          { label: '钢结构工程专业承包二级', value: '030743' },
          { label: '防水防腐保温工程专业承包一级', value: '030744' },
          { label: '防水防腐保温工程专业承包二级', value: '030745' },
          { label: '隧道工程专业承包三级', value: '030746' },
          { label: '预拌混凝土专业承包不分等级', value: '030747' },
        ],
      },
      {
        label: '房地产估价机构资质',
        value: '0308',
        children: [
          {
            label: '房地产估价机构信用档案',
            value: '030801',
          },
        ],
      },
      {
        label: '房地产开发企业资质',
        value: '0309',
      },
      {
        label: '设计施工一体化',
        value: '0310',
        children: [
          { label: '建筑智能化工程设计与施工二级', value: '031001' },
          { label: '建筑装饰装修工程设计与施工三级', value: '031002' },
          { label: '建筑装饰装修工程设计与施工二级', value: '031003' },
          { label: '消防设施工程设计与施工二级', value: '031004' },
          { label: '设计与施工一体化资质', value: '031005' },
          { label: '设计施工一体化企业资质证书', value: '031006' },
          { label: '设计施工一体化资质证书', value: '031007' },
        ],
      },
    ],
  },
  {
    label: 'CCC',
    value: '14',
    children: [
      {
        label: 'CCC',
        value: '1401',
      },
      {
        label: 'CCC产品认证',
        value: '1402',
      },
      {
        label: 'CCC工厂信息',
        value: '1403',
      },
      {
        label: 'CCC强制性产品查询',
        value: '1404',
      },
      {
        label: 'CCC证书',
        value: '1405',
      },
    ],
  },
  {
    label: '排污许可证',
    value: '04',
  },
  {
    label: '服务认证',
    value: '05',
    children: [
      { label: '一般服务认证', value: '0501' },
      { label: '体育场所服务认证', value: '0502' },
      { label: '信息安全服务资质认证', value: '0503' },
      { label: '商品售后服务评价认证', value: '0504' },
      { label: '所有未列明的其他服务认证', value: '0505' },
      { label: '汽车玻璃零配安装服务认证', value: '0506' },
      {
        label: '物业服务',
        value: '0507',
        children: [
          {
            label: '物业企业信息',
            value: '050701',
          },
        ],
      },
      { label: '绿色市场认证', value: '0508' },
      { label: '软件过程能力及成熟度评估认证', value: '0509' },
      { label: '防爆电器检修服务认证', value: '0510' },
    ],
  },
  {
    label: '服务资质认证证书',
    value: '06',
  },
  {
    label: '电信资质',
    value: '07',
    children: [
      {
        label: '电信业务经营许可证',
        value: '0701',
        children: [
          {
            label: '2006年度跨地区增值电信业务经营许可证书',
            value: '070101',
          },
          {
            label: '2006年度跨地区增值电信业务经营许可证年检不合格企业',
            value: '070102',
          },
          { label: '电信业务经营许可证审批', value: '070103' },
          { label: '电信网码号资源使用审批', value: '070104' },
        ],
      },
    ],
  },
  {
    label: '管理体系认证',
    value: '08',
    children: [
      { label: '中国共产党基层组织建设质量管理体系', value: '0801' },
      { label: '中国森林认证', value: '0802' },
      { label: '中国职业健康安全管理体系认证', value: '0803' },
      { label: '企业体系认证', value: '0804' },
      { label: '企业知识产权管理体系认证', value: '0805' },
      { label: '企业社会责任管理体系认证', value: '0806' },
      { label: '供应链安全管理体系认证', value: '0807' },
      { label: '信息安全管理体系认证', value: '0808' },
      { label: '信息技术服务管理体系认证', value: '0809' },
      { label: '其它管理体系认证', value: '0810' },
      { label: '医疗器械质量管理体系认证', value: '0811' },
      {
        label: '商品和服务在生命周期内的温室气体排放评价',
        value: '0812',
      },
      { label: '国际铁路行业质量管理体系认证', value: '0813' },
      { label: '建设施工行业质量管理体系认证', value: '0814' },
      { label: '德国汽车工业协会质量管理体系认证', value: '0815' },
      { label: '所有未列明的其他管理体系认证', value: '0816' },
      { label: '森林认证FSC', value: '0817' },
      { label: '森林认证PEFC', value: '0818' },
      { label: '汽车行业质量管理体系认证', value: '0819' },
      { label: '测量管理体系', value: '0820' },
      {
        label: '温室气体排放和清除的量化和报告的规范及指南认证',
        value: '0821',
      },
      { label: '环境管理体系认证', value: '0822' },
      {
        label: '电气与电子元件和产品有害物质过程控制管理体系认证',
        value: '0823',
      },
      { label: '电讯业质量管理体系认证', value: '0824' },
      { label: '能源管理体系认证', value: '0825' },
      { label: '航空业质量管理体系认证', value: '0826' },
      { label: '航空仓储销售商质量管理体系认证', value: '0827' },
      { label: '航空器维修质量管理体系认证', value: '0828' },
      { label: '质量管理体系认证(ISO9000)', value: '0829' },
      {
        label: '运输资产保护协会 运输供应商最低安全要求认证',
        value: '0830',
      },
      { label: '静电防护标准认证', value: '0831' },
      { label: '食品安全管理体系认证', value: '0832' },
      { label: '验证合格评定程序认证', value: '0833' },
    ],
  },
  {
    label: '自愿性产品认证',
    value: '09',
    children: [
      { label: '中国电子招标投标系统认证', value: '0901' },
      {
        label: '低碳产品认证',
        value: '0902',
        children: [
          {
            label: '低碳产品认证-通用硅酸盐水泥',
            value: '090201',
          },
        ],
      },
      {
        label: '信息安全产品认证(未列入强制性产品认证目录内的信息安全产品)',
        value: '0903',
      },
      { label: '光伏产品认证', value: '0904' },
      { label: '其他自愿性工业产品认证', value: '0905' },
      { label: '可再生能源', value: '0906' },
      { label: '城市轨道交通产品认证', value: '0907' },
      { label: '建筑节能产品认证', value: '0908' },
      { label: '环保产品认证', value: '0910' },
      { label: '环境标志产品', value: '0911' },
      { label: '环境标志产品认证', value: '0912' },
      { label: '电子信息产品污染控制自愿性认证', value: '0913' },
      { label: '节水产品认证', value: '0914' },
      { label: '节能产品认证(不含建筑节能)', value: '0915' },
      { label: '铁路产品认证', value: '0916' },
      { label: '防爆电气产品认证', value: '0917' },
      { label: '风电产品认证', value: '0918' },
    ],
  },
  {
    label: '食品农产品认证',
    value: '10',
    children: [
      { label: '中国食品农产品认证', value: '1001' },
      {
        label: '乳制品生产企业危害分析与关键控制点(HACCP)体系认证',
        value: '1002',
      },
      { label: '乳制品生产企业良好生产规范认证', value: '1003' },
      { label: '危害分析与关键控制点认证', value: '1004' },
      { label: '无公害农产品', value: '1005' },
      { label: '有机产品(OGA)', value: '1006' },
      { label: '有机产品认证', value: '1007' },
      { label: '良好农业规范(GAP)', value: '1008' },
      { label: '食品质量认证(酒类)', value: '1009' },
      { label: '饲料产品', value: '1010' },
    ],
  },
  {
    label: '食药资质',
    value: '11',
    children: [
      { label: '化妆品生产许可证', value: '1101' },
      { label: '医疗器械生产企业（备案）', value: '1102' },
      { label: '医疗器械生产企业（许可）', value: '1103' },
      {
        label: '医疗器械生产许可证(许可)',
        value: '1104',
        children: [
          {
            label: '医疗器械广告批准文号',
            value: '110401',
          },
        ],
      },
      { label: '医疗器械经营企业(备案)', value: '1105' },
      { label: '医疗器械经营企业(许可)', value: '1106' },
      { label: '国产医疗器械产品(备案)', value: '1107' },
      {
        label: '国产医疗器械产品(注册)',
        value: '1108',
        children: [
          {
            label: '国产器械注册号',
            value: '110801',
          },
        ],
      },
      { label: '进口医疗器械产品(备案)', value: '1109' },
      { label: '食品生产许可证', value: '1110' },
      { label: '食品经营许可证', value: '1111' },
    ],
  },
  {
    label: '高新技术企业',
    value: '12',
  },
  {
    label: '集成企业资质认证类型',
    value: '13',
  },
  {
    label: '其他',
    value: '99',
  },
];

// 社会组织状态码
export const npostatusmapping = {
  0: '暂无',
  10: '正常',
  20: '存续',
  30: '筹建',
  40: '清算',
  50: '迁入',
  60: '迁出',
  70: '停业',
  80: '撤销',
  90: '吊销',
  93: '其他',
  99: '注销',
  991: '申请',
  992: '已成立报批',
  993: '成立报批中',
  994: '成立中',
  995: '核名发起中',
  996: '核名未通过',
  997: '核名通过',
  998: '成立',
  999: '正常',
  9910: '注销',
  9911: '注销中',
  9912: '撤销',
  9913: '其他',
  9914: '暂无',
};

// 台湾企业状态码
export const taiwanstatusmapping = {
  100: '废止',
  101: '废止清算完结',
  102: '废止许可',
  103: '废止许可完结',
  104: '废止认许',
  105: '废止认许完结',
  106: '接管',
  107: '撤回认许',
  108: '撤回认许完结',
  110: '撤销设立',
  111: '撤销完结',
  112: '撤销无需清算',
  113: '撤销许可',
  114: '撤销认许',
  115: '撤销认许完结',
  116: '核准报备',
  117: '核准设立',
  118: '设立但已解散',
  119: '核准许可报备',
  120: '核准许可登记',
  121: '核准认许',
  122: '清理',
  123: '清理完结',
  124: '破产',
  125: '破产清算完结',
  126: '破产程序终结',
  127: '解散',
  128: '解散清算完结',
  129: '重整',
  130: '合并解散',
  131: '终止破产',
  132: '涂销破产',
  133: '核准许可',
  134: '核准登记',
  135: '分割解散',
  136: '废止登记完结',
  137: '废止登记',
  138: '撤销登记完结',
  139: '撤销登记',
  140: '撤回登记完结',
  141: '撤回登记',
  93: '其他',
};

// 香港公司状态码
export const hkstatusmapping = {
  92: '仍注册',
  93: '其他',
  94: '已告解散',
  95: '已终止营业地点',
  96: '不再是独立的实体',
  97: '休止活动',
};

export const shortstatuscodemapping = {
  10: '在业',
  20: '存续',
  30: '筹建',
  40: '清算',
  50: '迁入',
  60: '迁出',
  70: '停业',
  80: '撤销',
  90: '吊销',
  93: '其他',
  99: '注销',
};

export const OperInfoMapping = {
  Org: {
    // 公司
    Company: 0,
    // 社会组织
    Org: 1,
    // 主要人员
    Employee: 2,
    // 香港公司
    HKCompany: 3,
    // 政府机构和学校
    Government: 4,
    // 台湾公司
    TWCompany: 5,
    // 私募基金产品
    PefundProduct: 6,
    // 医院
    Hospital: 7,
    // 海外公司
    Oversea: 8,
    // 海外公司
    Oversea2: 9,
    // 基金会
    Fund: 10,
    // 事业单位
    Institution: 11,
    // 律师事务所
    LawOffice: 12,
    // 投资机构
    Invest: 13,
    // 美股
    UsStock: 14,
    // 无法判断
    CompanyWithoutKeyNo: -1,
    // 没有Id的人名
    PersonWithoutCerNo: -2,
    // 其他
    Other: -3,
  },
  OperType: {
    1: '法定代表人',
    2: '执行事务合伙人',
    3: '负责人',
    4: '经营者',
    5: '投资人',
    6: '董事长',
    7: '理事长',
    8: '代表人',
  },
};

export const MatchType = {
  phrase: 'phrase',
  bestFields: 'best_fields',
  crossFields: 'cross_fields',
};

// 查询策略模式
export const SearchMode = {
  /** 二次查询 */
  Secondary: 'secondary',

  /** 纯数字格式匹配的搜索关键词 */
  Numeric: 'numeric',
  /** 固定电话、手机格式匹配的搜索关键词 */
  Contact: 'contact',
  /** 网址格式匹配的关键词 */
  Website: 'website',
  /** 电子邮箱格式匹配的关键词 */
  Email: 'email', //电子邮箱"
  /** 中文名称格式匹配的关键词 */
  ChineseName: 'chinese-name',
  /** 英文你名称或拼音 */
  EnglishOrPinyin: 'english-or-pinyin',
  /** 中文名称格式匹配的关键词 */
  Category: 'category',
  /** 中文名称格式匹配的关键词 */
  ChineseIndustry: 'chinese-industry',
  /** 统一社会信用代码格式匹配的关键词 */
  Credit: 'credit',
  /** 省份名称匹配的关键词 */
  Province: 'province',
  /** 城市名称匹配的关键词 */
  City: 'city',
  /** 县、区名称匹配的关键词 */
  County: 'county',
  /** 常用词列表匹配的关键词 */
  Common: 'common',
  /** 组织匹配的关键词 */
  Organization: 'organization',
  /** 未知类型的关键词. */
  Unknown: 'unknown',
  /** 公司别名 */
  CompanyAbbr: 'company-abbr',
  /** 公司专有名词，用于精确查询 */
  ProperNouns: 'proper-nouns',
  /** 公司名称 */
  CompanyName: 'company-name',
  /** 行政区划 */
  Ad: 'ad',
  /** 行政区划 + 常用词 */
  AdCommon: 'ad+common',
  /**行政区划 + 组织"*/
  AdOrganization: 'ad+organization',
  /**常用词 + 组织"*/
  CommonOrganization: 'common+organization',
  /**行政区划 + 通用词 + 组织"*/
  AdCommonOrganization: 'ad+common+organization',
  /**商号"*/
  SH: 'sh',
  /** "商号 + 通用词"*/
  ShCommon: 'sh+common',
  /** "商号 + 组织"*/
  ShOrganization: 'sh+organization',
  /**商号 + 通用词 + 组织"*/
  SH_COMMON_ORGANIZATION: 'sh+common+organization',
  /** "行政区划 + 商号"*/
  AdSh: 'ad+sh',
  /**行政区划 + 商号 + 组织"*/
  AD_SH_ORGANIZATION: 'ad+sh+organization',
  /**行政区划 + 商号 + 通用词"*/
  AD_SH_COMMON: 'ad+sh+common',
  /**行政区划 + 商号 + 通用词 + 组织"*/
  AD_SH_COMMON_ORGANIZATION: 'ad+sh+common+organization',

  /**
   * 股票类型：新三板
   */
  STOCK_NEEQ: 'stock-neeq',

  /**
   * 股票类型：A股
   */
  STOCK_A: 'stock-a',

  /**
   * 股票类型：港股
   */
  STOCK_H: 'stock-h',

  /**
   * 股票类型：中概股
   */
  STOCK_CCS: 'stock-ccs',

  /**
   * 股票类型：新四板
   */
  STOCK_FOUR: 'stock-four',

  /**
   * 股票类型：科创板
   */
  STOCK_STAR: 'stock-star',

  /**
   * 整词由数字、字母、标点符号组成
   */
  ALPHANUMERIC_PUNCTUATION: 'alphanumeric-punctuation',

  /**
   * 中文名称格式匹配的关键词.
   */
  ENGLISH_OR_PINYIN: 'english-or-pinyin',
};

// 查询策略模式
export const SearchModeNoSecondarySearch = [
  SearchMode.Contact,
  SearchMode.Website,
  SearchMode.Email,
  SearchMode.Credit,
  SearchMode.Numeric,
  SearchMode.ENGLISH_OR_PINYIN,
];
