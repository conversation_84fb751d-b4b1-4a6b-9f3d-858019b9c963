export const QueryMode = {
  // 时间区间
  DateRange: 'DateRange',
  // 数字区间
  NumberRange: 'NumberRange',
  // 复选框
  Terms: 'Terms',
  FlagTerms: 'FlagTerms',
  // 文本输入
  MultiMatch: 'MultiMatch',
  Region: 'Region',
  Industry: 'Industry',
  Special: 'Special',
  GeoDistance: 'GeoDistance',
  GeoPolygon: 'GeoPolygon',
};

// 查询条件类
export class Condition {
  /** 条件入参key */
  key: string;
  /** 对应的es字段名 */
  fieldName: string;
  /** 对应的es文档 */
  doc: string;
  /** 查询匹配模式 */
  queryMode: string;
  /** 字段描述 */
  describe: string;
  /** 指定时区， 时间区间 字段可选 */
  timeZone?: string;
}

// 公司主表查询条件
export const CompanyConditionMapping = {
  r: {
    key: 'r',
    describe: '所属地区',
    fieldName: 'region',
    doc: 'company',
    queryMode: QueryMode.Region,
    pr: { key: 'r', describe: '省', fieldName: 'province', doc: 'company', queryMode: QueryMode.Terms },
    ac: { key: 'r', describe: '市/区县 ', fieldName: 'areacode', doc: 'company', queryMode: QueryMode.Terms },
  },
  i: {
    key: 'i',
    describe: '国民行业',
    fieldName: 'gmhy',
    doc: 'company',
    queryMode: QueryMode.Industry,
    ind: { key: 'i', describe: '行业大类', fieldName: 'industry', doc: 'company', queryMode: QueryMode.Terms },
    sub: { key: 'i', describe: '明细行业', fieldName: 'subind', doc: 'company', queryMode: QueryMode.Terms },
  },
  peoplec: {
    key: 'peoplec',
    describe: '人员规模',
    fieldName: 'peoplecount',
    doc: 'company',
    queryMode: QueryMode.Special,
    min: { key: 'peoplec', describe: '人员规模最小值', fieldName: 'peoplemincount', doc: 'company', queryMode: QueryMode.NumberRange },
    max: { key: 'peoplec', describe: '人员规模最大值', fieldName: 'peoplemaxcount', doc: 'company', queryMode: QueryMode.NumberRange },
  },
  map: {
    key: 'map',
    describe: '地图查询',
    fieldName: 'map',
    doc: 'company',
    queryMode: QueryMode.Special,
    distance: { key: 'map', describe: '圆形范围查询', fieldName: 'distance', doc: 'company', queryMode: QueryMode.GeoDistance },
    polygon: { key: 'map', describe: '不规则图形范围查询', fieldName: 'polygon', doc: 'company', queryMode: QueryMode.GeoPolygon },
  },
  b2bpc: { key: 'b2bpc', describe: 'b2b商品行业分类', fieldName: 'b2bproductcategory', doc: 'company', queryMode: QueryMode.Special },
  ltl: { key: 'ltl', describe: '最新纳税信用等级', fieldName: 'latesttaxlevel', doc: 'company', queryMode: QueryMode.Special },
  gxqy: { key: 'gxqy', describe: '高新企业', fieldName: 'tag', doc: 'company', queryMode: QueryMode.Special },
  flag: { key: 'flag', describe: 'FALG标签', fieldName: 'flag', doc: 'company', queryMode: QueryMode.FlagTerms },
  ci: { key: 'flag', describe: '联系方式(有手机 Flag.MN， 有固话 Flag.T，有邮箱 Flag.E)', fieldName: 'flag', doc: 'company', queryMode: QueryMode.FlagTerms },

  // ------------------ QueryMode.DateRange -------------- //
  sd: { key: 'sd', describe: '成立年限', fieldName: 'startdatecode', doc: 'company', queryMode: QueryMode.DateRange },
  lrd: { key: 'lrd', describe: '最新招聘日期', fieldName: 'latestrecruitmentdate', doc: 'company', queryMode: QueryMode.DateRange },
  tpd: { key: 'tpd', describe: '纳税人资格有效期', fieldName: 'taxpayerdate', doc: 'company', queryMode: QueryMode.DateRange },
  cd: { key: 'cd', describe: '变更日期', fieldName: 'changedate', doc: 'company', queryMode: QueryMode.DateRange },
  lcd: { key: 'lcd', describe: '最新变更日期', fieldName: 'latestchangedate', doc: 'company', queryMode: QueryMode.DateRange },
  lfd: { key: 'lfd', describe: '最新融资日期', fieldName: 'latestfinancingdate', doc: 'company', queryMode: QueryMode.DateRange },
  lpbd: { key: 'lpbd', describe: '最新地块公示日期', fieldName: 'lastpublicitydate', doc: 'company', queryMode: QueryMode.DateRange },
  lpcd: { key: 'lpcd', describe: '最新购地合同签订日期', fieldName: 'lastpurchasedate', doc: 'company', queryMode: QueryMode.DateRange },
  lmd: { key: 'lmd', describe: '最新土地转让成交日期', fieldName: 'lastmarketdate', doc: 'company', queryMode: QueryMode.DateRange },
  lid: { key: 'lid', describe: '最新对外投资日期', fieldName: 'lastinvestmentdate', doc: 'company', queryMode: QueryMode.DateRange },
  lprd: { key: 'lprd', describe: '最新产权交易起始日期', fieldName: 'lastproeprtydate', doc: 'company', queryMode: QueryMode.DateRange },
  lld: { key: 'lld', describe: '最新行政许可日期', fieldName: 'lastlicensedate', doc: 'company', queryMode: QueryMode.DateRange },
  led: { key: 'led', describe: '最新列入经营异常日期', fieldName: 'lastexceptionsdate', doc: 'company', queryMode: QueryMode.DateRange },
  lred: { key: 'lred', describe: '最新移出经营异常日期', fieldName: 'lastremoveexceptionsdate', doc: 'company', queryMode: QueryMode.DateRange },
  lpmd: { key: 'lpmd', describe: '最新行政处罚决定日期', fieldName: 'lastpunishmentdate', doc: 'company', queryMode: QueryMode.DateRange },
  lend: { key: 'lend', describe: '最新环保处罚日期', fieldName: 'lastenvpubdate', doc: 'company', queryMode: QueryMode.DateRange },
  lmtd: { key: 'lmtd', describe: '最新税收违法发布日期', fieldName: 'lastmajortaxillegaldate', doc: 'company', queryMode: QueryMode.DateRange },
  lmpd: { key: 'lmpd', describe: '最新动产抵押日期', fieldName: 'lastmpledgedate', doc: 'company', queryMode: QueryMode.DateRange },
  lad: { key: 'lad', describe: '最新司法拍卖时间', fieldName: 'lastauctionsdate', doc: 'company', queryMode: QueryMode.DateRange },
  levd: { key: 'levd', describe: '最新询价评估发布日期', fieldName: 'lastevaluationdate', doc: 'company', queryMode: QueryMode.DateRange },
  lmgd: { key: 'lmgd', describe: '最新土地抵押截止日期', fieldName: 'lastmortgagedate', doc: 'company', queryMode: QueryMode.DateRange },
  lpnd: { key: 'lpnd', describe: '最新公示催告公告日期', fieldName: 'lastpublicnoticedate', doc: 'company', queryMode: QueryMode.DateRange },
  ltod: { key: 'ltod', describe: '最新欠税公告发布日期', fieldName: 'lasttaxowedate', doc: 'company', queryMode: QueryMode.DateRange },
  lzd: { key: 'lzd', describe: '最新被执行人立案日期', fieldName: 'lastzxdate', doc: 'company', queryMode: QueryMode.DateRange },
  lxd: { key: 'lxd', describe: '最新失信发生日期', fieldName: 'lastsxdate', doc: 'company', queryMode: QueryMode.DateRange },
  leecd: { key: 'leecd', describe: '最新终本案件立案日期', fieldName: 'lastendexecutioncasedate', doc: 'company', queryMode: QueryMode.DateRange },
  lsd: { key: 'lsd', describe: '最新限制高消费发布日期', fieldName: 'lastsumptuarydate', doc: 'company', queryMode: QueryMode.DateRange },
  lchd: { key: 'lchd', describe: '最新刊登日期', fieldName: 'lastchinacourtdate', doc: 'company', queryMode: QueryMode.DateRange },
  lcod: { key: 'lcod', describe: '最新开庭公告开庭时间', fieldName: 'lastcourtnoticedate', doc: 'company', queryMode: QueryMode.DateRange },
  lrgd: { key: 'lrgd', describe: '最新诉讼日期', fieldName: 'lastregisterdate', doc: 'company', queryMode: QueryMode.DateRange },
  lcsd: { key: 'lcsd', describe: '最新裁判文书发布日期', fieldName: 'lastcasedate', doc: 'company', queryMode: QueryMode.DateRange },
  ocd: { key: 'ocd', describe: '初次获证日期', fieldName: 'oldcertificatedate', doc: 'company', queryMode: QueryMode.DateRange },
  lccd: { key: 'lccd', describe: '最新获证日期', fieldName: 'lastcertificatedate', doc: 'company', queryMode: QueryMode.DateRange },
  lrad: { key: 'lrad', describe: '最新信用评级日期', fieldName: 'lastratingdate', doc: 'company', queryMode: QueryMode.DateRange },
  lsvad: { key: 'lsvad', describe: '最新列入严重违法日期', fieldName: 'lastseriousviolationsdate', doc: 'company', queryMode: QueryMode.DateRange },
  leqd: { key: 'leqd', describe: '最新股权出质登记日期', fieldName: 'lastequitydate', doc: 'company', queryMode: QueryMode.DateRange },
  lpld: { key: 'lpld', describe: '最新股权质押公告日期', fieldName: 'lastpledgedate', doc: 'company', queryMode: QueryMode.DateRange },
  lbrd: { key: 'lbrd', describe: '破产重整公开日期', fieldName: 'lastbankruptcydate', doc: 'company', queryMode: QueryMode.DateRange },
  lmord: { key: 'lmord', describe: '最新土地抵押起始日期', fieldName: 'lastmortgagestartdate', doc: 'company', queryMode: QueryMode.DateRange },
  lasccd: { key: 'lasccd', describe: '最新刊登日期', fieldName: 'lastchinacourtdate', doc: 'company', queryMode: QueryMode.DateRange },
  lasld: { key: 'lasld', describe: '最新立案日期', fieldName: 'lastliandate', doc: 'company', queryMode: QueryMode.DateRange },
  csaiiibd: { key: 'csaiiibd', describe: '两化证书开始日期', fieldName: 'csaiiibegindate', doc: 'company', queryMode: QueryMode.DateRange },
  csaiiied: { key: 'csaiiied', describe: '两化证书结束日期', fieldName: 'csaiiienddate', doc: 'company', queryMode: QueryMode.DateRange },
  cld: { key: 'cld', describe: '注销日期', fieldName: 'cancellationdate', doc: 'company', queryMode: QueryMode.DateRange },
  rcd: { key: 'rcd', describe: '吊销日期', fieldName: 'revocationdate', doc: 'company', queryMode: QueryMode.DateRange },
  // ------------------ QueryMode.Terms -------------- //
  sc: { key: 'sc', describe: '经营状态', fieldName: 'statuscode', doc: 'company', queryMode: QueryMode.Terms },
  ru: { key: 'ru', describe: '资本类型', fieldName: 'registerunit', doc: 'company', queryMode: QueryMode.Terms },
  ekc: { key: 'ekc', describe: '企业类型', fieldName: 'econkindcode', doc: 'company', queryMode: QueryMode.Terms },
  ot: { key: 'ot', describe: '组织机构', fieldName: 't_type', doc: 'company', queryMode: QueryMode.Terms },
  fl: { key: 'fl', describe: '融资阶段', fieldName: 'financinglevel', doc: 'company', queryMode: QueryMode.Terms },
  lst: { key: 'lst', describe: '上市板块', fieldName: 'listingstatus', doc: 'company', queryMode: QueryMode.Terms },
  tag: { key: 'tag', describe: '新兴行业', fieldName: 'tag', doc: 'company', queryMode: QueryMode.Terms },
  ps: { key: 'ps', describe: '手机来源渠道', fieldName: 'phonesource', doc: 'company', queryMode: QueryMode.Terms },
  ts: { key: 'ts', describe: '固话来源渠道', fieldName: 'telsource', doc: 'company', queryMode: QueryMode.Terms },
  es: { key: 'es', describe: '邮箱来源渠道', fieldName: 'emailsource', doc: 'company', queryMode: QueryMode.Terms },
  lty: { key: 'lty', describe: '最新纳税信用评价年度', fieldName: 'latesttaxyear', doc: 'company', queryMode: QueryMode.Terms },
  ipa: { key: 'ipa', describe: '商标地址待变更', fieldName: 'invalidtrademarkaddress', doc: 'company', queryMode: QueryMode.Terms },
  qccl: { key: 'qccl', describe: '企查查认证等级', fieldName: 'qccauthenticationlevel', doc: 'company', queryMode: QueryMode.Terms },
  csaiiis: { key: 'csaiiis', describe: '两化状态 0未接触 1接触 2启动 3完成', fieldName: 'csaiiistatus', doc: 'company', queryMode: QueryMode.Terms },
  reporty: { key: 'reporty', describe: '最新年报年份', fieldName: 'reportyear', doc: 'company', queryMode: QueryMode.Terms },
  bat: { key: 'bat', describe: '疑似代记账号码', fieldName: 'billingagenttype', doc: 'company', queryMode: QueryMode.Terms },
  coml: { key: 'coml', describe: '信用评级', fieldName: 'comratinglevel', doc: 'company', queryMode: QueryMode.Terms },
  tpt: { key: 'tpt', describe: '纳税人资质', fieldName: 'taxpayertypes', doc: 'company', queryMode: QueryMode.Terms },
  ppsr: { key: 'ppsr', describe: '独资企业地区 (美国、法国等)', fieldName: 'proprietorshipregion', doc: 'company', queryMode: QueryMode.Terms },
  qxql: { key: 'qxql', describe: '企业潜力', fieldName: 'flag', doc: 'company', queryMode: QueryMode.Terms },
  zjrhc: { key: 'zjrhc', describe: '营收水平万元', fieldName: 'revenuehierarchy', doc: 'company', queryMode: QueryMode.Terms },
  zjphc: { key: 'zjphc', describe: '利润水平万元', fieldName: 'profithierarchy', doc: 'company', queryMode: QueryMode.Terms },
  zjasc: { key: 'zjasc', describe: '资产规模', fieldName: 'assetshierarchy', doc: 'company', queryMode: QueryMode.Terms },

  // ------------------ QueryMode.NumberRange -------------- //
  rca: { key: 'rca', describe: '注册资本', fieldName: 'registcapiamount', doc: 'company', queryMode: QueryMode.NumberRange },
  ic: { key: 'ic', describe: '参保人数', fieldName: 'insuredcount', doc: 'company', queryMode: QueryMode.NumberRange },
  pos: { key: 'pos', describe: '岗位名称', fieldName: 'position', doc: 'company', queryMode: QueryMode.NumberRange },
  plc: { key: 'plc', describe: '当前招聘人数', fieldName: 'personnelcount', doc: 'company', queryMode: QueryMode.NumberRange },
  pnc: { key: 'pnc', describe: '招聘岗位总数', fieldName: 'positionscount', doc: 'company', queryMode: QueryMode.NumberRange },
  fr: { key: 'fr', describe: '融资次数', fieldName: 'financinground', doc: 'company', queryMode: QueryMode.NumberRange },
  lfa: { key: 'lfa', describe: '最新融资金额', fieldName: 'latestfinancingamount', doc: 'company', queryMode: QueryMode.NumberRange },
  ld: { key: 'ld', describe: '上市日期', fieldName: 'listdate', doc: 'company', queryMode: QueryMode.NumberRange },
  adlc: { key: 'adlc', describe: '行政许可数量', fieldName: 'adlicensecount', doc: 'company', queryMode: QueryMode.NumberRange },
  mec: { key: 'mec', describe: '经营异常数量', fieldName: 'manageexceptioncount', doc: 'company', queryMode: QueryMode.NumberRange },
  pc: { key: 'pc', describe: '行政处罚数量', fieldName: 'punishcount', doc: 'company', queryMode: QueryMode.NumberRange },
  vpc: { key: 'vpc', describe: '有效状态商标数量', fieldName: 'validpatentcount', doc: 'company', queryMode: QueryMode.NumberRange },
  ipc: { key: 'ipc', describe: '无效状态商标数量', fieldName: 'invalidpatentcount', doc: 'company', queryMode: QueryMode.NumberRange },
  lyppc: { key: 'lyppc', describe: '最近一年公开专利数量', fieldName: 'lypubpatentcount', doc: 'company', queryMode: QueryMode.NumberRange },
  cretc: { key: 'cretc', describe: '证书数量', fieldName: 'cretcount', doc: 'company', queryMode: QueryMode.NumberRange },
  tcretc: { key: 'tcretc', describe: '本年度获证数量', fieldName: 'tcretcount', doc: 'company', queryMode: QueryMode.NumberRange },
  coprc: { key: 'coprc', describe: '作品著作权数量', fieldName: 'coprcount', doc: 'company', queryMode: QueryMode.NumberRange },
  scoprc: { key: 'scoprc', describe: '软件著作权数量', fieldName: 'scoprcount', doc: 'company', queryMode: QueryMode.NumberRange },
  icpc: { key: 'icpc', describe: 'ICP备案数量', fieldName: 'icpcount', doc: 'company', queryMode: QueryMode.NumberRange },
  afa: { key: 'afa', describe: '总融资金额', fieldName: 'allfinancingamount', doc: 'company', queryMode: QueryMode.NumberRange },
  ia: { key: 'ia', describe: '对外投资金额', fieldName: 'investmentamount', doc: 'company', queryMode: QueryMode.NumberRange },
  pac: { key: 'pac', describe: '专利数量', fieldName: 'patentcount', doc: 'company', queryMode: QueryMode.NumberRange },
  rcpa: { key: 'rcpa', describe: '实缴资本金额数字(万元)', fieldName: 'reccapamount', doc: 'company', queryMode: QueryMode.NumberRange },
  yysra: { key: 'yysra', describe: '上市企业年度营业额(万元)', fieldName: 'yysramount', doc: 'company', queryMode: QueryMode.NumberRange },
  iprpc: { key: 'iprpc', describe: '知识产权出质总数', fieldName: 'iprightpledgecount', doc: 'company', queryMode: QueryMode.NumberRange },
  ckc: { key: 'ckc', describe: '企业浏览量', fieldName: 'clickcount', doc: 'company', queryMode: QueryMode.NumberRange },
  ospc: { key: 'ospc', describe: '国际专利数量', fieldName: 'overseapatentcount', doc: 'company', queryMode: QueryMode.NumberRange },
  fct: { key: 'fct', describe: '商业特许经营数量', fieldName: 'franchisecount', doc: 'company', queryMode: QueryMode.NumberRange },
  // ------------------ QueryMode.MultiMatch -------------- //
  name: { key: 'name', describe: '企业名称', fieldName: 'name', doc: 'company', queryMode: QueryMode.MultiMatch },
  originalname: { key: 'originalname', describe: '曾用名', fieldName: 'originalname', doc: 'company', queryMode: QueryMode.MultiMatch },
  intr: { key: 'intr', describe: '企业简介', fieldName: 'introduction', doc: 'company', queryMode: QueryMode.MultiMatch },
  scope: { key: 'scope', describe: '经营范围', fieldName: 'scope', doc: 'company', queryMode: QueryMode.MultiMatch },
  address: { key: 'address', describe: '注册地址', fieldName: 'address', doc: 'company', queryMode: QueryMode.MultiMatch },
  address2: { key: 'address2', describe: '年报地址', fieldName: 'address2', doc: 'company', queryMode: QueryMode.MultiMatch },
  lct: { key: 'lct', describe: '最新变更类型', fieldName: 'latestchangetype', doc: 'company', queryMode: QueryMode.MultiMatch },
  pro: { key: 'pro', describe: '主营产品', fieldName: 'product', doc: 'company', queryMode: QueryMode.MultiMatch },
  ws: { key: 'ws', describe: '网站名称网址', fieldName: 'website', doc: 'company', queryMode: QueryMode.MultiMatch },
  wsinfo: { key: 'wsinfo', describe: '网站关键词', fieldName: 'websitetitleinfos', doc: 'company', queryMode: QueryMode.MultiMatch },
  b2bp: { key: 'b2bp', describe: 'b2b商品名称', fieldName: 'b2bproduct', doc: 'company', queryMode: QueryMode.MultiMatch },
  courtnames: { key: 'courtnames', describe: '法院名称', fieldName: 'courtnames', doc: 'company', queryMode: QueryMode.MultiMatch },
  //   ct: { key: 'ct', describe: '变更类型', fieldName: 'changetype', doc: 'company', queryMode: QueryMode.MultiMatch }
};

// 招投标 子表数据条件
export const TenderConditionMapping = {
  tt: { key: 'tt', describe: '招投标项目名称', fieldName: 'tender.title', doc: 'tender', queryMode: QueryMode.MultiMatch },
  tr: { key: 'tr', describe: '招投标项目角色(招标单位、投标单位、代理单位)', fieldName: 'tender.role', doc: 'tender', queryMode: QueryMode.Terms },
  tdpd: { key: 'tdpd', describe: '招投标发布时间', fieldName: 'tender.publishdate', doc: 'tender', queryMode: QueryMode.DateRange },
  tdod: { key: 'tdod', describe: '开标时间', fieldName: 'tender.opendate', doc: 'tender', queryMode: QueryMode.DateRange },
  tdbv: { key: 'tdbv', describe: '招投标项目预算', fieldName: 'tender.budgetvalue', doc: 'tender', queryMode: QueryMode.NumberRange },
  tdifbp: { key: 'tdifbp', describe: '招投标项目阶段', fieldName: 'tender.ifbprogress', doc: 'tender', queryMode: QueryMode.Terms },
  tdi: { key: 'tdi', describe: '招投标项目所属行业', fieldName: 'tender.industry', doc: 'tender', queryMode: QueryMode.Terms },
  tdr: {
    key: 'tdr',
    describe: '招投标项目地区',
    fieldName: 'tender.region',
    doc: 'tender',
    queryMode: QueryMode.Region,
    pr: { key: 'pr', describe: '省', fieldName: 'tender.province', doc: 'tender', queryMode: QueryMode.Terms },
    ac: { key: 'ac', describe: '城市', fieldName: 'tender.city', doc: 'tender', queryMode: QueryMode.Terms },
  },
  tdwtbif: { key: 'tdwtbif', describe: '中标单位json', fieldName: 'tender.wtbinfo', doc: 'tender', queryMode: QueryMode.Special },
  tdifbif: { key: 'tdifbif', describe: '招标单位json', fieldName: 'tender.ifbinfo', doc: 'tender', queryMode: QueryMode.Special },
  tdans: { key: 'tdans', describe: '代理单位名称', fieldName: 'tender.agentnames', doc: 'tender', queryMode: QueryMode.MultiMatch },
  tdaif: { key: 'tdaif', describe: '代理单位json', fieldName: 'tender.agentinfo', doc: 'tender', queryMode: QueryMode.Special },
  tdifbpr: { key: 'tdifbpr', describe: '招投标项目结果', fieldName: 'tender.ifbprogress', doc: 'tender', queryMode: QueryMode.Terms },
  tdwtbns: { key: 'tdwtbns', describe: '中标单位名称', fieldName: 'tender.wtbnames', doc: 'tender', queryMode: QueryMode.MultiMatch },
  tdifbns: { key: 'tdifbns', describe: '招标单位名称', fieldName: 'tender.ifbnames', doc: 'tender', queryMode: QueryMode.MultiMatch },
  tdwtba: { key: 'tdwtba', describe: '招投标中标金额 万元', fieldName: 'tender.amount', doc: 'tender', queryMode: QueryMode.NumberRange },

  // // 有无拟建项目	Y-有拟建项目、N-无拟建项目
  // tdnj: 'tender.ifbprogress',
  // // 拟建项目名称		tender.title
  // tdnjt: 'tender.title',
  // // 拟建项目发布时间		tender.publishdate
  // tdnjpd: 'tender.publishdate',
  // // 招投标行业  tender.industry
  // tdnji: 'tender.industry',
  // // 拟建项目地区 tender.city/tender.province
  // tdnjr: {
  //   pr: '省code',
  //   ac: '市code'
  // },
  // // 拟建项目批复结果 'tender.ifbprogress'
  // tdnjifbpr: 'tender.ifbprogress'
};

// 工商变更 子表数据条件
export const CompanyChangeConditionMapping = {
  cccd: { key: 'cccd', describe: '变更日期', fieldName: 'companychange.changedate', doc: 'companychange', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  ccpn: { key: 'ccpn', describe: '变更类型', fieldName: 'companychange.projectname', doc: 'companychange', queryMode: QueryMode.MultiMatch },
  ccfct: {
    key: 'ccfct',
    describe: '浮动类型 0:不变 1:下降 2:上升',
    fieldName: 'companychange.floatchangetype',
    doc: 'companychange',
    queryMode: QueryMode.Terms,
  },
  ccfpt: { key: 'ccfpt', describe: '浮动百分比', fieldName: 'companychange.floatpercentage', doc: 'companychange', queryMode: QueryMode.NumberRange },
  ccfabs: { key: 'ccfabs', describe: '浮动绝对值(万元人民币)', fieldName: 'companychange.floatabs', doc: 'companychange', queryMode: QueryMode.NumberRange },
  cccg: { key: 'cccg', describe: '变更类型(枚举)', fieldName: 'companychange.category', doc: 'companychange', queryMode: QueryMode.Terms },
  ccsbi: { key: 'ccsbi', describe: '经营范围增项', fieldName: 'companychange.scopebeforeinfos', doc: 'companychange', queryMode: QueryMode.MultiMatch },
  ccafi: { key: 'ccafi', describe: '经营范围减项', fieldName: 'companychange.scopeafterinfos', doc: 'companychange', queryMode: QueryMode.MultiMatch },
};

// 司法案件 子表数据条件
export const CompanyCaseConditionMapping = {
  ccct: { key: 'ccct', describe: '司法案件类型', fieldName: 'companycase.casetype', doc: 'companycase', queryMode: QueryMode.Terms },
  ccrt: { key: 'ccrt', describe: '司法案件身份', fieldName: 'companycase.roletype', doc: 'companycase', queryMode: QueryMode.Terms },
  cclsl: { key: 'cclsl', describe: '司法案件进程', fieldName: 'companycase.latesttrialround', doc: 'companycase', queryMode: QueryMode.Terms },
  cccre: { key: 'cccre', describe: '司法案件案由', fieldName: 'companycase.casereason', doc: 'companycase', queryMode: QueryMode.MultiMatch },
};

// 经营异常 子表数据条件
export const ExceptionConditionMapping = {
  er: { key: 'er', describe: '经营异常原因数据整理', fieldName: 'exception.reason', doc: 'exception', queryMode: QueryMode.DateRange },
  ert: { key: 'ert', describe: '经营异常原因(类型数据整理)', fieldName: 'exception.type', doc: 'exception', queryMode: QueryMode.Terms },
  ea: { key: 'ea', describe: '列入经营异常名录原因', fieldName: 'exception.addreason', doc: 'exception', queryMode: QueryMode.MultiMatch },
  ead: { key: 'ead', describe: '列入经营异常日期', fieldName: 'exception.adddate', doc: 'exception', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  erd: { key: 'erd', describe: '移出经营异常日期', fieldName: 'exception.removedate', doc: 'exception', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  err: { key: 'err', describe: '移出经营异常名录原因', fieldName: 'exception.romovereason', doc: 'exception', queryMode: QueryMode.MultiMatch },
};

// 证书 子表数据条件
export const CretConditionMapping = {
  crt: { key: 'crt', describe: '涵盖证书类别', fieldName: 'cret.type', doc: 'cret', queryMode: QueryMode.Terms },
  crtn: { key: 'crtn', describe: '证书类型三级分类', fieldName: 'cret.name', doc: 'cret', queryMode: QueryMode.MultiMatch },
  crtl1n: { key: 'crtl1n', describe: '证书类型一级分类', fieldName: 'cret.level1name', doc: 'cret', queryMode: QueryMode.MultiMatch },
  crtl2n: { key: 'crtl2n', describe: '证书类型二级分类', fieldName: 'cret.level2name', doc: 'cret', queryMode: QueryMode.MultiMatch },
  crn: { key: 'crn', describe: '证书名称', fieldName: 'cret.primaryproductname', doc: 'cret', queryMode: QueryMode.MultiMatch },
  crs: { key: 'crs', describe: '证书发证日期', fieldName: 'cret.startdate', doc: 'cret', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  ced: { key: 'ced', describe: '证书有效截止日期', fieldName: 'cret.enddate', doc: 'cret', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  cltd: { key: 'cltd', describe: '资质证书最新截止日期', fieldName: 'cret.latestdate', doc: 'cret', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  cretn: { key: 'cretn', describe: '证书编号 "cret.no" 分词字段 "cret.nos"', fieldName: 'cret.nos.text', doc: 'cret', queryMode: QueryMode.MultiMatch },
  cretbo: { key: 'cretbo', describe: '证书颁发机构', fieldName: 'cret.beloneorg.text', doc: 'cret', queryMode: QueryMode.MultiMatch },
  crets: {
    key: 'crets',
    describe: '证书状态0其他、1有效、2过期、3失效、4撤销、5变更、6暂停、7正常、8注销等',
    fieldName: 'cret.status',
    doc: 'cret',
    queryMode: QueryMode.Terms,
  },
};

// 立案 子表数据条件
export const RegisterConditionMapping = {
  rgsrd: { key: 'rgsrd', describe: '立案日期', fieldName: 'register.riskdate', doc: 'register', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  rgst: { key: 'rgst', describe: '立案当事人', fieldName: 'register.type', doc: 'register', queryMode: QueryMode.Terms },
  rgscn: { key: 'rgscn', describe: '立案法院名称', fieldName: 'register.courtname', doc: 'register', queryMode: QueryMode.MultiMatch },
  rgsn: { key: 'rgsn', describe: '案件号', fieldName: 'register.caseno', doc: 'register', queryMode: QueryMode.DateRange },
  rescr: { key: 'rescr', describe: '案件原因', fieldName: 'register.casereason', doc: 'register', queryMode: QueryMode.DateRange },
};

// 招聘 子表数据条件
export const RecruitmentConditionMapping = {
  rect: { key: 'rect', describe: '岗位名称', fieldName: 'recruitment.title', doc: 'recruitment', queryMode: QueryMode.MultiMatch },
  red: { key: 'red', describe: '岗位描述', fieldName: 'recruitment.description', doc: 'recruitment', queryMode: QueryMode.MultiMatch },
  res: { key: 'res', describe: '招聘渠道', fieldName: 'recruitment.source', doc: 'recruitment', queryMode: QueryMode.MultiMatch },
  recs: { key: 'recs', describe: '岗位薪酬（月薪）', fieldName: 'recruitment.salaryvalue', doc: 'recruitment', queryMode: QueryMode.NumberRange },
  recp: { key: 'recp', describe: '工作地址', fieldName: 'recruitment.province', doc: 'recruitment', queryMode: QueryMode.MultiMatch },
  recegc: {
    key: 'recegc',
    describe: '岗位学历(本科、专科、研究生、高中、初中、小学)',
    fieldName: 'recruitment.edugroupcode',
    doc: 'recruitment',
    queryMode: QueryMode.Terms,
  },
  recexgc: { key: 'recexgc', describe: '岗位经验', fieldName: 'recruitment.expgroupcode', doc: 'recruitment', queryMode: QueryMode.Terms },
  recpd: {
    key: 'recpd',
    describe: '岗位发布时间',
    fieldName: 'recruitment.publishdate',
    doc: 'recruitment',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  // 招聘地址
  recr: {
    key: 'recr',
    describe: '招聘地址',
    fieldName: 'recregion',
    doc: 'recruitment',
    queryMode: QueryMode.Region,
    pr: { key: 'pr', describe: '招聘地址省', fieldName: 'recruitment.province', doc: 'recruitment', queryMode: QueryMode.Terms },
    ac: { key: 'ac', describe: '招聘地址区县 ', fieldName: 'recruitment.areacode', doc: 'recruitment', queryMode: QueryMode.Terms },
  },
};

// 开庭公告 子表数据条件
export const CourtnoticeConditionMapping = {
  cnld: { key: 'cnld', describe: '开庭公告开庭时间', fieldName: 'courtnotice.liandate', doc: 'courtnotice', queryMode: QueryMode.DateRange },
  cncr: { key: 'cncr', describe: '开庭公告案由', fieldName: 'courtnotice.casereason', doc: 'courtnotice', queryMode: QueryMode.MultiMatch },
  cnt: {
    key: 'cnt',
    describe: '开庭公告当事人(公诉人、原告、上诉人、申请人、被告人、被告、被上诉人、被申请人)',
    fieldName: 'courtnotice.type',
    doc: 'courtnotice',
    queryMode: QueryMode.Terms,
  },
  cneg: { key: 'cneg', describe: '开庭公告的法院名称', fieldName: 'courtnotice.executegov', doc: 'courtnotice', queryMode: QueryMode.MultiMatch },
};

// 终本案件 子表数据条件
export const EndexecutioncaseConditionMapping = {
  eecjd: {
    key: 'eecjd',
    describe: '终本案件立案日期',
    fieldName: 'endexecutioncase.judgedate',
    doc: 'endexecutioncase',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  eeced: {
    key: 'eeced',
    describe: '终本案件终本日期',
    fieldName: 'endexecutioncase.enddate',
    doc: 'endexecutioncase',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  eedfa: { key: 'eedfa', describe: '终本案件未履行金额', fieldName: 'endexecutioncase.failureact', doc: 'endexecutioncase', queryMode: QueryMode.NumberRange },
  eecpc: { key: 'eecpc', describe: '终本案件原告', fieldName: 'endexecutioncase.prosecutor.text', doc: 'endexecutioncase', queryMode: QueryMode.MultiMatch },
  eecda: { key: 'eecda', describe: '终本案件被告', fieldName: 'endexecutioncase.defendant.text', doc: 'endexecutioncase', queryMode: QueryMode.MultiMatch },
};

// 专利 子表数据条件
export const PatentConditionMapping = {
  pkc: { key: 'pkc', describe: '专利类型	发明专利、实用新型专利、外观设计专利', fieldName: 'patent.kindcode', doc: 'patent', queryMode: QueryMode.Terms },
  pt: { key: 'pt', describe: '专利名称', fieldName: 'patent.title', doc: 'patent', queryMode: QueryMode.MultiMatch },
  ppk: { key: 'ppk', describe: '专利公开（公告）日期', fieldName: 'patent.pubdate', doc: 'patent', queryMode: QueryMode.DateRange },
  pts: { key: 'pts', describe: '法律状态', fieldName: 'patent.status', doc: 'patent', queryMode: QueryMode.Terms },
  pa: { key: 'pa', describe: '专利代理机构', fieldName: 'patent.agency', doc: 'patent', queryMode: QueryMode.MultiMatch },
};

// 专利数量统计 按类型统计子表
export const PatentTypeConditionMapping = {
  ptfmgb: { key: 'ptfmgb', describe: '发明公布 专利数量统计', fieldName: 'patenttype.fmgb', doc: 'patenttype', queryMode: QueryMode.NumberRange },
  ptfmsq: { key: 'ptfmsq', describe: '发明授权', fieldName: 'patenttype.fmsq', doc: 'patenttype', queryMode: QueryMode.NumberRange },
  ptsyxx: { key: 'ptsyxx', describe: '实用新型', fieldName: 'patenttype.syxx', doc: 'patenttype', queryMode: QueryMode.NumberRange },
  ptwgsj: { key: 'ptwgsj', describe: '外观设计', fieldName: 'patenttype.wgsj', doc: 'patenttype', queryMode: QueryMode.NumberRange },
};

// 专利数量统计 按年份统计子表
export const PatentYearConditionMapping = {
  ptyear: { key: 'ptyear', describe: '专利年份', fieldName: 'patentyear.year', doc: 'patentyear', queryMode: QueryMode.DateRange },
  ptcnt: { key: 'ptcnt', describe: '专利数量', fieldName: 'patentyear.cnt', doc: 'patentyear', queryMode: QueryMode.DateRange },
};

//国际专利
export const OverseaPatentConditionMapping = {
  ospt: { key: 'ospt', describe: '国际专利名称', fieldName: 'overseapatent.title', doc: 'overseapatent', queryMode: QueryMode.MultiMatch },
  ospad: { key: 'ospad', describe: '专利申请日期', fieldName: 'overseapatent.applicationdate', doc: 'overseapatent', queryMode: QueryMode.DateRange },
  osppid: { key: 'osppid', describe: '专利公开（公告）日期', fieldName: 'overseapatent.publicationdate', doc: 'overseapatent', queryMode: QueryMode.DateRange },
  ospi: { key: 'ospi', describe: '国际专利发明人', fieldName: 'overseapatent.inventors', doc: 'overseapatent', queryMode: QueryMode.MultiMatch },
  opa: { key: 'opa', describe: '代理机构', fieldName: 'overseapatent.agency', doc: 'overseapatent', queryMode: QueryMode.DateRange },
};

// 商业特许经营 维度，即连锁店维度
export const FranchisePatentConditionMapping = {
  fifn: { key: 'fifn', describe: '特许人名称', fieldName: 'franchise.franchisorname', doc: 'franchise', queryMode: QueryMode.MultiMatch },
  fird: { key: 'fird', describe: '备案公示日期', fieldName: 'franchise.recorddate', doc: 'franchise', queryMode: QueryMode.DateRange },
};

// 代理机构(知识产权行业)合作企业子维度
export const CooperationConditionMapping = {
  cckn: { key: 'ccn', describe: '合作企业keyno', fieldName: 'cooperation.companykeyno', doc: 'cooperation', queryMode: QueryMode.DateRange },
  ccn: { key: 'ccn', describe: '合作企业名称', fieldName: 'cooperation.companyname', doc: 'cooperation', queryMode: QueryMode.DateRange },
  ccot: { key: 'ccot', describe: '合作类型', fieldName: 'cooperation.cooptype', doc: 'cooperation', queryMode: QueryMode.DateRange },
  ccpc: { key: 'ccpc', describe: '合作次数', fieldName: 'cooperation.coopcount', doc: 'cooperation', queryMode: QueryMode.DateRange },
  ccpak: { key: 'ccpak', describe: '代理机构keyno', fieldName: 'cooperation.agentkeyno', doc: 'cooperation', queryMode: QueryMode.Terms },
  ccpan: { key: 'ccpan', describe: '代理机构名称', fieldName: 'cooperation.agentname', doc: 'cooperation', queryMode: QueryMode.DateRange },
};

// 商标文书 trademarkdetail
export const TrademarkDetailConditionMapping = {
  tadt: { key: 'tadt', describe: '商标文书标题', fieldName: 'trademarkdetail.title', doc: 'trademarkdetail', queryMode: QueryMode.DateRange },
  tadj: { key: 'tadj', describe: '商标文书裁定/决定文书号', fieldName: 'trademarkdetail.judgeno', doc: 'trademarkdetail', queryMode: QueryMode.DateRange },
  tadan: { key: 'tadan', describe: '商标文书申请人', fieldName: 'trademarkdetail.applyname.text', doc: 'trademarkdetail', queryMode: QueryMode.MultiMatch },
  tadran: {
    key: 'tadran',
    describe: '商标文书被申请人',
    fieldName: 'trademarkdetail.reapplyname.text',
    doc: 'trademarkdetail',
    queryMode: QueryMode.MultiMatch,
  },
  tadpn: { key: 'tadpn', describe: '商标文书代理人', fieldName: 'trademarkdetail.proxyname.text', doc: 'trademarkdetail', queryMode: QueryMode.MultiMatch },
  tadrpn: {
    key: 'tadrpn',
    describe: '商标文书代理人(被申请)',
    fieldName: 'trademarkdetail.reproxyname.text',
    doc: 'trademarkdetail',
    queryMode: QueryMode.MultiMatch,
  },
  tadpd: {
    key: 'tadpd',
    describe: '商标文书公布日期',
    fieldName: 'trademarkdetail.publishdate',
    doc: 'trademarkdetail',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  tadok: { key: 'tadok', describe: '商标文书展示的osskey', fieldName: 'trademarkdetail.osskey', doc: 'trademarkdetail', queryMode: QueryMode.DateRange },
  tadakn: { key: 'tadakn', describe: '商标文书申请人keynos', fieldName: 'trademarkdetail.applykeynos', doc: 'trademarkdetail', queryMode: QueryMode.DateRange },
  tadrapkn: {
    key: 'tadrapkn',
    describe: '商标文书被申请人keynos',
    fieldName: 'trademarkdetail.reapplykeynos',
    doc: 'trademarkdetail',
    queryMode: QueryMode.DateRange,
  },
  tadpkn: { key: 'tadpkn', describe: '商标文书代理人keynos', fieldName: 'trademarkdetail.proxykeynos', doc: 'trademarkdetail', queryMode: QueryMode.DateRange },
  tadrpkn: {
    key: 'tadrpkn',
    describe: '商标文书被代理人keynos',
    fieldName: 'trademarkdetail.reproxykeynos',
    doc: 'trademarkdetail',
    queryMode: QueryMode.DateRange,
  },
  tadtt: {
    key: 'tadtt',
    describe: '商标文书的类型  商标异议决定书 商标注册审查决定书(未使用)  商标裁定书',
    fieldName: 'trademarkdetail.tmtype',
    doc: 'trademarkdetail',
    queryMode: QueryMode.Terms,
  },
  tadttc: {
    key: 'tadttc',
    describe: '商标文书的类型code(3-特殊商标; 6-证明商标; 7-普通商标; 11-集体商标;)',
    fieldName: 'trademarkdetail.tmtypecode',
    doc: 'cooperation',
    queryMode: QueryMode.DateRange,
  },
};

// 海关 子表数据条件
export const CustomsConditionMapping = {
  cr: { key: 'cr', describe: '进出口信用注册日期', fieldName: 'customs.regdate', doc: 'customs', queryMode: QueryMode.DateRange },
  ctt: { key: 'ctt', describe: '海关登记经营类别', fieldName: 'customs.tradetype', doc: 'customs', queryMode: QueryMode.Terms },
  // crg: { key: 'crg', describe: '海关信用类型', fieldName: 'customs.reggov', doc: 'customs', queryMode: QueryMode.DateRange },
  cit: { key: 'cit', describe: '海关登记行业', fieldName: 'customs.industrytype', doc: 'customs', queryMode: QueryMode.MultiMatch },
  ccf: { key: 'ccf', describe: '进出口信用状态', fieldName: 'customs.cancellationflag', doc: 'customs', queryMode: QueryMode.Terms },
};

/** --------------------------------- 购地信息 子表数据条件 -------------- */
export const PurchaseConditionMapping = {
  blsct: {
    key: 'blsct',
    describe: '购地合同签订日期',
    fieldName: 'purchase.signcontracttime',
    doc: 'purchase',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  bllu: { key: 'bllu', describe: '购地信息土地用途', fieldName: 'purchase.landuse', doc: 'purchase', queryMode: QueryMode.MultiMatch },
};

/** --------------------------------- 行政许可 子表数据条件 -------------- */
export const LicenseConditionMapping = {
  ln: { key: 'ln', describe: '行政许可文件名称', fieldName: 'license.name', doc: 'license', queryMode: QueryMode.MultiMatch },
  lc: { key: 'lc', describe: '行政许可内容', fieldName: 'license.content', doc: 'license', queryMode: QueryMode.MultiMatch },
  licld: {
    key: 'licld',
    describe: '行政许可有效期,有效期自与有效期至任何一个值在所选择的区间即满足展示条件',
    fieldName: 'license.liandate',
    doc: 'license',
    queryMode: QueryMode.DateRange,
  },
  liceg: { key: 'liceg', describe: '行政许可机关', fieldName: 'license.executegov', doc: 'license', queryMode: QueryMode.MultiMatch },
  licc: { key: 'licc', describe: '行政许可内容', fieldName: 'license.content', doc: 'license', queryMode: QueryMode.DateRange },
  liclr: { key: 'liclr', describe: '行政许可状态', fieldName: 'license.lawsuitresult2', doc: 'license', queryMode: QueryMode.Terms },
};

/** --------------------------------- 裁判文书 子表数据条件 -------------- */
export const CaseConditionMapping = {
  cc: { key: 'cc', describe: '裁判文书案由', fieldName: 'case.casereason', doc: 'case', queryMode: QueryMode.MultiMatch },
  casen: { key: 'casen', describe: '裁判文书标题', fieldName: 'case.name', doc: 'case', queryMode: QueryMode.MultiMatch },
  casesd: { key: 'casesd', describe: '裁判文书发布日期', fieldName: 'case.submitdate', doc: 'case', queryMode: QueryMode.DateRange },
  caset: { key: 'caset', describe: '裁判文书案件身份', fieldName: 'case.type', doc: 'case', queryMode: QueryMode.Terms },
  caseai: { key: 'caseai', describe: '裁判文书案件金额', fieldName: 'case.amountinvolved', doc: 'case', queryMode: QueryMode.NumberRange },
};

/** --------------------------------- 法院公告 子表数据条件 -------------- */
export const ChinacourtConditionMapping = {
  ccc: { key: 'ccc', describe: '法院公告类型', fieldName: 'chinacourt.categorycode', doc: 'chinacourt', queryMode: QueryMode.Terms },
  cct: { key: 'cct', describe: '法院公告当事人', fieldName: 'chinacourt.type', doc: 'chinacourt', queryMode: QueryMode.Terms },
  cccr: { key: 'cccr', describe: '法院公告案由', fieldName: 'chinacourt.casereason', doc: 'chinacourt', queryMode: QueryMode.MultiMatch },
  ccpd: { key: 'ccpd', describe: '刊登日期', fieldName: 'chinacourt.publishdate', doc: 'chinacourt', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 商标 子表数据条件 -------------- */
export const TrademarkConditionMapping = {
  tn: { key: 'tn', describe: '商标名', fieldName: 'trademark.name', doc: 'trademark', queryMode: QueryMode.MultiMatch },
  tv: { key: 'tv', describe: '商标专用权结束日期', fieldName: 'trademark.validperiod', doc: 'trademark', queryMode: QueryMode.DateRange },
  tfs: {
    key: 'tfs',
    describe: '商标流程状态(商标被驳回、商标进行形式审查、商标进行实质审查、商标驳回复审通过、商标注册存在异议、商标公告、商标注册成功)',
    doc: 'trademark',
    fieldName: 'trademark.flowstatus',
    queryMode: QueryMode.Terms,
  },
  tad: { key: 'tad', describe: '商标申请日期', fieldName: 'trademark.appdate', doc: 'trademark', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  ti: { key: 'ti', describe: '商标国际分类', fieldName: 'trademark.intcls', doc: 'trademark', queryMode: QueryMode.Terms },
  tsg: { key: 'tsg', describe: '商标内容', fieldName: 'trademark.similargroups', doc: 'trademark', queryMode: QueryMode.MultiMatch },
  tra: { key: 'tra', describe: '商标代理机构', fieldName: 'trademark.agency', doc: 'trademark', queryMode: QueryMode.MultiMatch },
};

/** --------------------------------- 作品 子表数据条件 -------------- */
export const CoprConditionMapping = {
  cn: { key: 'cn', describe: '作品名称', fieldName: 'copr.name', doc: 'copr', queryMode: QueryMode.MultiMatch },
  copd: { key: 'copd', describe: '作品首次发表日期', fieldName: 'copr.publishdate', doc: 'copr', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  cofd: { key: 'cofd', describe: '作品创作完成日期', fieldName: 'copr.finishdate', doc: 'copr', queryMode: QueryMode.DateRange },
  cord: { key: 'cord', describe: '作品登记日期', fieldName: 'copr.registerdate', doc: 'copr', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  cot: { key: 'cot', describe: '作品登记类别', fieldName: 'copr.type', doc: 'copr', queryMode: QueryMode.Terms },
};

/** --------------------------------- 软件著作权 子表数据条件 -------------- */
export const ScoprConditionMapping = {
  scn: { key: 'scn', describe: '软件名称', fieldName: 'scopr.name', doc: 'scopr', queryMode: QueryMode.MultiMatch },
  corad: { key: 'corad', describe: '软件著作权登记日期', fieldName: 'scopr.registerapprdate', doc: 'scopr', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 地块公示 子表数据条件 -------------- */
export const PublicityConditionMapping = {
  ppd: { key: 'ppd', describe: '地块公示发布日期', fieldName: 'publicity.publishdate', doc: 'publicity', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 土地转让 子表数据条件 -------------- */
export const DealConditionMapping = {
  dtt: { key: 'dtt', describe: '土地转让成交日期', fieldName: 'deal.transactiontime', doc: 'deal', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 信用评级 子表数据条件 -------------- */
export const RatingConditionMapping = {
  ron: { key: 'ron', describe: '信用评级机构', fieldName: 'rating.orgname', doc: 'rating', queryMode: QueryMode.MultiMatch },
  rrd: { key: 'rrd', describe: '信用评级日期', fieldName: 'rating.ratingdate', doc: 'rating', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  rcl: { key: 'rcl', describe: '信用评级级别', fieldName: 'rating.comratinglevel', doc: 'rating', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 产权交易 子表数据条件 -------------- */
export const TransactionConditionMapping = {
  ttn: { key: 'ttn', describe: '产权交易标的名称', fieldName: 'transaction.targetname', doc: 'transaction', queryMode: QueryMode.MultiMatch },
  ttrp: { key: 'ttrp', describe: '产权交易价格', fieldName: 'transaction.transferreserveprice', doc: 'transaction', queryMode: QueryMode.NumberRange },
  tsd: { key: 'tsd', describe: '产权交易起始日期', fieldName: 'transaction.startdate', doc: 'transaction', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 电信许可 子表数据条件 -------------- */
export const TelicenseConditionMapping = {
  tls: { key: 'tls', describe: '电信许可业务范围', fieldName: 'telicense.scope', doc: 'telicense', queryMode: QueryMode.MultiMatch },
  tli: { key: 'tli', describe: '电信许可状态	有效、无效', fieldName: 'telicense.isok', doc: 'telicense', queryMode: QueryMode.Terms },
};

/** ---------------------------------  列入严重违法 子表数据条件 -------------- */
export const SeriousviolationsConditionMapping = {
  svad: {
    key: 'svad',
    describe: '列入严重违法日期',
    fieldName: 'seriousviolations.adddate',
    doc: 'seriousviolations',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  svar: {
    key: 'svar',
    describe: '列入严重违法企业名单原因',
    fieldName: 'seriousviolations.addreason',
    doc: 'seriousviolations',
    queryMode: QueryMode.MultiMatch,
  },
};

/** --------------------------------- 股权出质 子表数据条件 -------------- */
export const EquityConditionMapping = {
  eqs: { key: 'eqs', describe: '股权出质状态	有效、无效', fieldName: 'equity.status', doc: 'equity', queryMode: QueryMode.Terms },
  eqpa: { key: 'eqpa', describe: '出质股权数额（万元）', fieldName: 'equity.pledgedamount', doc: 'equity', queryMode: QueryMode.NumberRange },
  eqrd: { key: 'eqrd', describe: '股权出质登记日期', fieldName: 'equity.regdate', doc: 'equity', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 股权质押 子表数据条件 -------------- */
export const PledgeConditionMapping = {
  plfr: { key: 'plfr', describe: '股权质押原因', fieldName: 'pledge.frozenreason', doc: 'pledge', queryMode: QueryMode.MultiMatch },
  plpp: { key: 'plpp', describe: '股权质押目的', fieldName: 'pledge.pledgepur', doc: 'pledge', queryMode: QueryMode.MultiMatch },
  plsd: { key: 'plsd', describe: '股权质押开始日期', fieldName: 'pledge.startdate', doc: 'pledge', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  pled: { key: 'pled', describe: '股权质押解除日期', fieldName: 'pledge.enddate', doc: 'pledge', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  plt: { key: 'plt', describe: '股权质押状态	未达预警线、已解除质押', fieldName: 'pledge.type', doc: 'pledge', queryMode: QueryMode.Terms },
  plnd: { key: 'plnd', describe: '股权质押公告日期', fieldName: 'pledge.noticedate', doc: 'pledge', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 行政处罚 子表数据条件 -------------- */
export const PunishConditionMapping = {
  punprt: { key: 'punprt', describe: '行政处罚类型', fieldName: 'punish.punish_reason_type', doc: 'punish', queryMode: QueryMode.Terms },
  punpd: { key: 'punpd', describe: '行政处罚决定日期', fieldName: 'punish.punish_date', doc: 'punish', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  punpr: { key: 'punpr', describe: '行政处罚事由', fieldName: 'punish.punish_reason', doc: 'punish', queryMode: QueryMode.MultiMatch },
  punpre: { key: 'punpre', describe: '行政处罚结果', fieldName: 'punish.punish_result', doc: 'punish', queryMode: QueryMode.MultiMatch },
};
/** --------------------------------- 环保处罚 子表数据条件 -------------- */
export const EnvironmentalConditionMapping = {
  envpunr: { key: 'envpunr', describe: '环保处罚事由', fieldName: 'environmental.punishreason', doc: 'environmental', queryMode: QueryMode.MultiMatch },
  envit: { key: 'envit', describe: '环保违法类型', fieldName: 'environmental.illegaltype', doc: 'environmental', queryMode: QueryMode.Terms },
  envpb: { key: 'envpb', describe: '环保处罚依据', fieldName: 'environmental.punishbasis', doc: 'environmental', queryMode: QueryMode.MultiMatch },
  envpr: { key: 'envpr', describe: '环保处罚结果', fieldName: 'environmental.punishmentresult', doc: 'environmental', queryMode: QueryMode.MultiMatch },
  envpd: { key: 'envpd', describe: '环保处罚日期', fieldName: 'environmental.punishdate', doc: 'environmental', queryMode: QueryMode.DateRange },
  envi: { key: 'envi', describe: '环保处罚执行情况', fieldName: 'environmental.implementation', doc: 'environmental', queryMode: QueryMode.MultiMatch },
};

/** --------------------------------- 税收违法 子表数据条件 -------------- */
export const TaxConditionMapping = {
  taxpt: { key: 'taxpt', describe: '税收违法发布日期', fieldName: 'tax.publishtime', doc: 'tax', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  taxcn: { key: 'taxcn', describe: '税收违法案件性质', fieldName: 'tax.casenature', doc: 'tax', queryMode: QueryMode.MultiMatch },
  taxic: { key: 'taxic', describe: '税收违法事实及处罚情况', fieldName: 'tax.illegalcontent', doc: 'tax', queryMode: QueryMode.MultiMatch },
};

/** --------------------------------- 动产抵押 子表数据条件 -------------- */
export const ChattelConditionMapping = {
  chs: { key: 'chs', describe: '动产抵押状态', fieldName: 'chattel.status', doc: 'chattel', queryMode: QueryMode.Terms },
  chrd: { key: 'chrd', describe: '动产抵押登记日期', fieldName: 'chattel.registerdate', doc: 'chattel', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  chsd: { key: 'chsd', describe: '动产抵押开始日期', fieldName: 'chattel.startdate', doc: 'chattel', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  ched: { key: 'ched', describe: '动产抵押结束日期', fieldName: 'chattel.enddate', doc: 'chattel', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  cha: { key: 'cha', describe: '动产抵押债权金额', fieldName: 'chattel.amount', doc: 'chattel', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 司法拍卖 子表数据条件 -------------- */
export const JudicialConditionMapping = {
  jun: { key: 'jun', describe: '司法拍卖标题', fieldName: 'judicial.name', doc: 'judicial', queryMode: QueryMode.MultiMatch },
  juam: { key: 'juam', describe: '司法拍卖时间', fieldName: 'judicial.actionremark', doc: 'judicial', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 询价评估 子表数据条件 -------------- */
export const EvaluationConditionMapping = {
  evat: { key: 'evat', describe: '询价评估标的物', fieldName: 'evaluation.target', doc: 'evaluation', queryMode: QueryMode.MultiMatch },
  evp: { key: 'evp', describe: '询价评估发布日期', fieldName: 'evaluation.publicdate2', doc: 'evaluation', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 土地抵押 子表数据条件 -------------- */
export const MortgageConditionMapping = {
  morsd: { key: 'morsd', describe: '土地抵押起始日期', fieldName: 'mortgage.starttime', doc: 'mortgage', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  mored: { key: 'mored', describe: '土地抵押截止日期', fieldName: 'mortgage.endtime', doc: 'mortgage', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
  morma: { key: 'morma', describe: '土地抵押面积（公顷）', fieldName: 'mortgage.mortgageacreage', doc: 'mortgage', queryMode: QueryMode.NumberRange },
  mormp: { key: 'mormp', describe: '土地抵押金额（万元）', fieldName: 'mortgage.mortgageprice', doc: 'mortgage', queryMode: QueryMode.NumberRange },
};

/** --------------------------------- 公示催告 子表数据条件 -------------- */
export const PublicnoticConditionMapping = {
  pubp: { key: 'pubp', describe: '公示催告票面金额', fieldName: 'publicnotic.pmmoneyint', doc: 'publicnotic', queryMode: QueryMode.NumberRange },
  pubpt: { key: 'pubpt', describe: '公示催告票据类型(汇票、本票、支票)', fieldName: 'publicnotic.pjtype', doc: 'publicnotic', queryMode: QueryMode.Terms },
  pubpd: { key: 'pubpd', describe: '公示催告公告日期', fieldName: 'publicnotic.publishdt', doc: 'publicnotic', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 欠税 子表数据条件 -------------- */
export const OweConditionMapping = {
  owet: { key: 'owet', describe: '欠税税种', fieldName: 'owe.title', doc: 'owe', queryMode: QueryMode.MultiMatch },
  owea: { key: 'owea', describe: '欠税余额（元）', fieldName: 'owe.amt', doc: 'owe', queryMode: QueryMode.NumberRange },
  owen: { key: 'owen', describe: '最新发生的欠税金额（元）', fieldName: 'owe.newamt', doc: 'owe', queryMode: QueryMode.NumberRange },
  owepd: { key: 'owepd', describe: '欠税公告发布日期', fieldName: 'owe.publishdate', doc: 'owe', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 被执行人 子表数据条件 -------------- */
export const ZhixingConditionMapping = {
  zxld: { key: 'zxld', describe: '被执行人立案日期', fieldName: 'zhixing.liandate', doc: 'zhixing', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 失信被执行人 子表数据条件 -------------- */
export const ShixinConditionMapping = {
  sxld: { key: 'sxld', describe: '失信被执行人立案日期', fieldName: 'shixin.liandate', doc: 'shixin', queryMode: QueryMode.DateRange },
  sxpd: { key: 'sxpd', describe: '失信被执行人发布日期', fieldName: 'shixin.publicdate', doc: 'shixin', queryMode: QueryMode.DateRange },
};

/** --------------------------------- 限制高消费 子表数据条件 -------------- */
export const SumptuaryConditionMapping = {
  suppd: {
    key: 'suppd',
    describe: '限制高消费发布日期',
    fieldName: 'sumptuary.publishdate',
    doc: 'sumptuary',
    queryMode: QueryMode.DateRange,
    timeZone: '+08:00',
  },
  jd: { key: 'jd', describe: '限制高消费立案日期', fieldName: 'sumptuary.judgedate', doc: 'sumptuary', queryMode: QueryMode.DateRange, timeZone: '+08:00' },
};

/** --------------------------------- 股权冻结 子表数据条件 -------------- */
export const AssistanceConditionMapping = {
  // asea: { key: 'asea', describe: '股权冻结数量', fieldName: 'assistance.equityamount',doc: 'assistance', queryMode: QueryMode.NumberRange },
  ass: {
    key: 'ass',
    describe: '股权冻结状态(股权冻结|冻结，股权冻结|解除冻结)',
    fieldName: 'assistance.status',
    doc: 'assistance',
    queryMode: QueryMode.Terms,
  },
};

/**
 * 搜索字段映射
 */
export const ConditionMapping = {
  // 主表条件
  ...CompanyConditionMapping,

  // 招投标 子表数据条件
  ...TenderConditionMapping,

  // 工商变更 子表数据条件
  ...CompanyChangeConditionMapping,

  // 司法案件 子表数据条件
  ...CompanyCaseConditionMapping,

  // 经营异常 子表数据条件
  ...ExceptionConditionMapping,

  // 证书 子表数据条件
  ...CretConditionMapping,

  // 立案 子表数据条件
  ...RegisterConditionMapping,

  // 招聘 子表数据条件
  ...RecruitmentConditionMapping,

  // 开庭公告 子表数据条件
  ...CourtnoticeConditionMapping,

  // 终本案件 子表数据条件
  ...EndexecutioncaseConditionMapping,

  //  专利 子表数据条件
  ...PatentConditionMapping,

  // 专利数量统计 按类型统计子表
  ...PatentTypeConditionMapping,

  // 国际专利
  ...OverseaPatentConditionMapping,

  //商业特许经营维度，即连锁店维度
  ...FranchisePatentConditionMapping,

  // 代理机构(知识产权行业)合作企业子维度
  ...CooperationConditionMapping,

  // 商标文书 trademarkdetail
  ...TrademarkDetailConditionMapping,
};
