import { keys } from 'lodash';

export const KysSearchFieldMapping = {
  //企业名称
  name: ['name^1', 'originalname^0.8', 'englishname^0.1'],
  //企业产品
  product: ['product^2', 'tag^0.5'],
  //业务范围
  scope: ['scope^0.6', 'introduction^0.6', 'tag^0.5'],
  // //商标
  // trademark: ['featurelist^1'],
  // //专利
  patent: ['patent^0.5'],
  // //招投标  分词
  // tender: ['tendertitleall^0.1'],
  //网址
  website: ['website^0.5'],
  // //招聘岗位  分词
  // position: ['position^0.1'],
  //地址  分词
  address: ['address^0.3', 'address2^0.3'],
};

/** 普通搜索需要分词匹配的字段 */
export const FuzzyFields = {
  name: ['namesearch', 'name.text^0.8'],
  website: ['website^0.1'],
};

/**
 * 关键字查询匹配可选字段
 */
export const KysSearchIndex = keys(KysSearchFieldMapping);

export const UseinfoCategory = {
  Customer: 'customer',
  Leads: 'leads',
  Unlock: 'unlock',
};

/**
 * 高级搜索中需要参与打分的字段
 */
export const NeedMustFields = [
  'name',
  'scope',
  'intr',
  'pro',
  'ws',
  'address',
  'pos',
  'cc',
  'ea',
  'scn',
  'cn',
  'crn',
  'tn',
  'pt',
  'ls',
  'lc',
  'ln',
  'tr',
  'tt',
];

export const KysDefaultIncludeFields = [
  'name', // 客户信息 企业名称
  'opername', // 法人
  'operinfo',
  'statuscode',
  'status', // 客户信息 经营状态
  // 'flag',
  'creditcode', // 统一社会信用代码
  'address', // 注册地址
  'address2', //年报地址
  'registcapiamount',
  'registcapi', // 注册资本
  'startdateyear',
  'startdatecode', // 成立日期
  'insuredcount', // 企业规模
  'econkindcode',
  'econkind', // 企业类型
  'province',
  'provincedesc',
  'areacode', // 区域
  'industry',
  'subind', // 行业
  // 'financinglevel', // 融资阶段
  'reccap', // 实缴资本,
  'reccapamount', // 实缴资本金额数字(万元)
  'hasimage',
];

export const ApiDefaultIncludeFields = [
  'id',
  'name', // 客户信息 企业名称
  'industry',
  'subind', // 行业
  // 'registcapiamount', // 注册资本金额数字
  'registcapi', // 注册资本
  'reccap', // 实缴资本,
  // 'reccapamount', // 实缴资本金额数字(万元)
  'startdatecode', // 成立日期
  'province',
  'provincedesc',
  // 'areacode', // 区域
  'lastenvpubdate', // 最新环保处罚日期
  'statuscode',
  'status', // 客户信息 经营状态
  'tellist',
  'location',
  'address', // 注册地址
  'econkindcode', // 企业类型code
  'econkind', // 企业类型code
  // 固话

  // 手机
];

/**
 * 排序可选字段
 */
export const KysSortFields = ['startdatecode', 'registcapiamount', 'weight'];

/**
 * 客户排序可选字段
 * 创建时间, 最后跟进时间
 */
export const KysCustomerSortFields = ['createDate', 'trackingUpdateDate'];

/**
 * 普通搜索过滤项
 * */
export const GeneralFilter = ['ci', 'sc', 'r', 'i', 'rca', 'ru', 'sd', 'ekc', 'ot', 'ic', 'flag', 'tag', 'polygon', 'distance', 'qccl'];

// 子表flag映射
export const ChildIndexMap = {
  // 商标信息
  trademark: 'M',
  // 专利信息
  patent: 'P',
  // 招投标信息
  tender: 'TE',
  // 经营异常
  exception: 'EP',
  // 作品著作权
  copr: 'C',
  // 软件著作权
  scopr: 'SC',
  // 证书信息
  cret: 'ZS',
  // 行政许可
  license: 'XZXK',
  // 进出口信用
  customs: 'CI',
  // 电信许可
  telicense: 'LIC',
  // 裁判文书
  case: 'CPWS',
  // 法院公告
  chinacourt: 'FYGG',
  // 地块公示
  publicity: 'LP',
  // 土地转让
  deal: 'LT',
  // 信用评级
  rating: 'XYPJ',
  // 产权交易
  transaction: 'CQJY',
  // 严重违法
  seriousviolations: 'YZWF',
  // 股权出质
  equity: 'PL',
  // 股权质押
  pledge: 'GQZY',
  // 行政处罚
  punish: 'AOP',
  // 环保处罚
  environmental: 'ENP',
  // 税收违法
  tax: 'TAB',
  // 动产抵押
  chattel: 'MP',
  // 司法拍卖
  judicial: 'SFPW',
  // 询价评估
  evaluation: 'XJPG',
  // 土地抵押
  mortgage: 'TDDY',
  // 公示催告
  publicnotic: 'GSCG',
  // 欠税公告
  owe: 'QSGG',
  // 有无被执行人信息
  zhixing: 'ZX',
  // 失信被执行人
  shixin: 'S',
  // 终本案件
  endexecutioncase: 'EC',
  // 限制高消费
  sumptuary: 'XG',
  // 股权冻结
  assistance: 'GQDJ',
  // 开庭公告
  courtnotice: 'KTGG',
  // 招聘
  recruitment: 'RE',
  // 购地信息
  purchase: 'BL',
  // 立案信息
  register: 'LA',
  // 工商变更
  companychange: 'CR',
  // 司法案件
  companycase: 'SFAJ',
  // 国际专利
  overseapatent: 'KZZ_OPC',
  // 商业特许经营
  franchise: 'KZZ_CFC',
  // 商标文书
  trademarkdetail: 'KZZ_HAVE_IPRIGHT_JUDG',

  // -----------其他子表
  // 专利数量统计 按类型统计子表
  patenttype: '',
  // 专利数量统计 按年份统计子表
  patentyear: '',
};

// 选项常量
export const KysSearchParams = {
  // 号码 status 1 可用， 2 不可用  3 未知
  // 原始状态 0：空号, 1：实号, 2：停机, 3：库无, 4：沉默号, 5：风险号
  telStatus: {
    1: '可用', // 可用: 1:实号
    2: '不可用', // 不可用：0: 空号、2:停机、4:沉默号、5:风险号
    3: '未知', // 未知：3:库无 或其他未罗列出来的状态
  },
  //  经营状态 statuscode
  statusCode: {
    10: '在业', // 正常
    20: '存续',
    30: '筹建',
    40: '清算',
    50: '迁入',
    60: '迁出',
    70: '停业',
    80: '撤销',
    90: '吊销',
    99: '注销',
    93: '其他',
    92: '仍注册',
    94: '已告解散',
    95: '已终止营业地点',
    96: '不再是独立的实体',
    97: '休止活动',
    100: '废止',
    101: '废止清算完结',
    102: '废止许可',
    103: '废止许可完结',
    104: '废止认许',
    105: '废止认许完结',
    106: '接管',
    107: '撤回认许',
    108: '撤回认许完结',
    110: '撤销设立',
    111: '撤销完结',
    112: '撤销无需清算',
    113: '撤销许可',
    114: '撤销认许',
    115: '撤销认许完结',
    116: '核准报备',
    117: '核准设立',
    118: '设立但已解散',
    119: '核准许可报备',
    120: '核准许可登记',
    121: '核准认许',
    122: '清理',
    123: '清理完结',
    124: '破产',
    125: '破产清算完结',
    126: '破产程序终结',
    127: '解散',
    128: '解散清算完结',
    129: '重整',
    130: '合并解散',
    131: '终止破产',
    132: '涂销破产',
    133: '核准许可',
    134: '核准登记',
    135: '分割解散',
    136: '废止登记完结',
    137: '废止登记',
    138: '撤销登记完结',
    139: '撤销登记',
    140: '撤回登记完结',
    141: '撤回登记',
  },
  //  资本类型 registerunit
  registCapitalUnit: {
    CNY: '人民币',
    TWD: '新台币',
    USD: '美元',
    HKD: '港元',
    EUR: '欧元',
    JPY: '日元',
    OTHER: '其他',
  },
  //  企业类型 econkindcode,
  econKindCode: {
    10: '有限责任公司',
    20: '股份有限公司',
    30: '国企',
    40: '外商投资企业',
    50: '独资企业',
    70: '个体工商户',
    80: '联营企业',
    90: '集体所有制',
    100: '有限合伙',
    110: '普通合伙',
  },
  // 组织机构 type
  orgType: {
    0: '大陆企业',
    1: '社会组织',
    3: '香港企业',
    4: '除基金会、事业单位、律师事务所之外的',
    5: '台湾公司',
    10: '基金会',
    11: '事业单位',
    12: '律师事务所',
    21: '美股企业',
  },
  //  融资阶段 financinglevel
  // 0-无融资信息、
  financeStageType: {
    1: '天使/种子轮',
    2: 'Pre-A至A+轮',
    3: 'Pre-B至B+轮',
    4: 'C轮及以上',
    5: '新三板/上市',
    6: '收购/并购/被并购',
    7: '战略投资',
    8: '其他',
  },
  // listingstatus 上市板块
  listingInfo: {
    '-1': '未上市',
    1: '新三板',
    // 2: 'A股', 已拆分成  21   主板   22   中小板  23  创业板
    21: '主板',
    22: '中小板',
    23: '创业板',
    6: '港股',
    7: '中概股',
    301: '新四板',
    501: '科创板',
  },
  companyChangeCategory: [
    '名称变更',
    '股东变更',
    '法人变更',
    '投资人变更',
    '负债人变更',
    '经营者变更',
    '股权/转让变更',
    '经营场所变更',
    '经营范围变更',
    '经营期限变更',
    '联系方式变更',
    '企业类型变更',
    '注册资本变更',
    '其他',
  ],
  //  企业潜力 flag
  qyql: {
    HT001: '高新技术企业',
    UE: '独角兽企业',
    GE: '瞪羚企业',
    YEE: '雏鹰企业',
    ITE: '创新型科技企业',
    STE: '科技小巨人企业',
    SSE: '专精特新企业',
    T_TSMES: '科技型中小企业',
    T_PT: '民营科技企业',
    T_TC: '企业技术中心',
    T_MS: '国家备案众创空间',
    T_CI: '科技企业孵化器',
    T_TD: '技术创新示范企业',
    T_IC: '隐形冠军企业',
    T_ATS: '技术先进型服务企业',
    T_AC: '牛羚企业',
    XWQY: '小微企业',
  },
  flag: {
    T: '有固话',
    MN: '有手机',
    E: '有邮箱',
    YR: '有无年报',
    B: '有无分支机构',
    GT: '是否一般纳税人',
    // NT: '纳税人资格类型(A级/非A级)',
    CR: '有无变更信息',
    RE: '有无招聘信息',
    IM: '有无控股企业',
    FIV: '有无投资机构',
    SMV: '有无私募基金',
    TF: '是否500强企业',
    TE: '有无招投标',
    NJX: '有无拟建项目',
    CI: '有无进出口信用',
    F: '有无融资信息',
    K: '是否上市企业',
    LP: '有无地块公示',
    BL: '有无购地信息',
    LT: '有无土地转让',
    IVM: '有无对外投资',
    BM: '有无建筑资质',
    XZXK: '有无行政许可',
    EP: '有无经营异常',
    YZWF: '有无严重违法',
    GQZY: '有无股权质押',
    AOP: '有无行政处罚',
    ENP: '有无环保处罚',
    TAB: '有无税收违法',
    SFPW: '有无司法拍卖',
    XJPG: '有无询价评估',
    PCCZ: '有无破产重整',
    TDDY: '有无土地抵押',
    GSCG: '有无公示催告',
    QSGG: '有无欠税公告',
    ZX: '有无被执行人信息',
    S: '有无失信被执行人',
    EC: '有无终本案件',
    XG: '有无限制高消费',
    CPWS: '有无裁判文书',
    FYGG: '有无法院公告',
    KTGG: '有无开庭公告',
    GQDJ: '有无股权冻结/有无司法协助',
    LA: '有无立案信息',
    ZS: '有无证书',
    C: '有无作品著作权',
    SC: '有无软件著作权',
    TELB: '有无ICP备案',
    APP: '有无APP',
    WP: '有无小程序',
    WB: '有无微博',
    GZH: '有无微信公众号',
    LIC: '有电信许可',
    GW: '有无官网',
    P: '有专利',
    M: '有商标',
    MP: '有无动产抵押',
    PL: '有股权出质',
    UE: '独角兽企业',
    GE: '瞪羚企业',
    YEE: '雏鹰企业',
    ITE: '创新型科技企业',
    STE: '科技小巨人企业',
    SSE: '专精特新企业',
    T_TSMES: '科技型中小企业',
    T_PT: '民营科技企业',
    T_TC: '企业技术中心',
    T_MS: '国家备案众创空间',
    T_CI: '科技企业孵化器',
    T_TD: '技术创新示范企业',
    T_IC: '隐形冠军企业',
    T_ATS: '技术先进型服务企业',
    T_AC: '牛羚企业',
    XWQY: '小微企业',
    HT001: '高新技术企业',
    TZJG: '有无投资机构',
    SFAJ: '有无司法案件',
    CQJY: '有无产权交易',
    XYPJ: '有无信用评级',
    JYZX: '简易注销',
    KZZ_IPS: '有无知识产权出质',
    KZZ_YQ: '是否央企',
    KZZ_NO_NTM: '无失信人',
    KZZ_PART_NTM: '部分失信',
    KZZ_ALL_NTM: '全部失信',
    KZZ_NO_EM: ' 无可执行',
    KZZ_PART_EM: '部分可执行',
    KZZ_ALL_EM: '全部可执行',
    KZZ_NO_LH: '无限高',
    KZZ_PART_LH: '部分限高',
    KZZ_ALL_LH: '全部限高',
    KZZ_OPC: '有无国际专利',
    KZZ_APP_NOT_REGIST: 'app未注册商标',
    KZZ_IS_AGENCY: '是代理机构',
    KZZ_PROP_SHIP: '是独资企业',
    KZZ_CFC: '商业特许经营',
    KZZ_PATENT_NAME_CHANGE: '专利关联主体名称变更',
    KZZ_SOFT_NAME_CHANGE: '软著关联主体名称变更',
    KZZ_HAVE_IPRIGHT_JUDG: '是否有商标文书',
  },
  contract: {
    T: '有无固话',
    MN: '有无手机',
    E: '有无邮箱',
  },
  tenderIndustry: {
    1: '建筑工程',
    2: '行政办公',
    3: '医疗卫生',
    4: '服务采购',
    5: '机械设备',
    6: '水利水电',
    7: '能源化工',
    8: '弱电安防',
    9: '信息技术',
    10: '交通工程',
    11: '市政设施',
    12: '农林牧渔',
    13: '政府采购',
    14: '药品采购',
    15: '其他',
  },
  tenderIfbprogress: {
    1: '拟建项目',
    3: '招标公告',
    4: '中标结果',
    5: '其他',
    31: '招标',
    32: '邀标',
    33: '竞谈',
    34: '询价',
    35: '变更',
    36: '竞价',
    37: '其他',
    38: '预告',
    39: '竞磋',
    41: '开标',
    42: '中标',
    43: '成交',
    44: '变更',
    45: '终止',
    46: '废标',
    47: '流标',
    49: '其他',
    10: '其他',
    12: '审批未通过',
    13: '审批通过',
    14: '审批中',
    15: '未审批',
    16: '撤销',
    48: '合同及验收',
    310: '单一来源',
    311: '澄清答疑',
    312: '资审',
  },
  patenStatus: {
    '01': '有效',
    '02': '失效',
    '03': '审中',
    '04': '未确认',
    '0101': '授权',
    '0102': '权利恢复',
    '0103': '部分无效',
    '0201': '全部无效',
    '0202': '驳回',
    '0203': '权利终止',
    '0204': '撤回',
    '0205': '避重授权',
    '0206': '未缴年费',
    '0207': '期限届满',
    '0208': '放弃',
    '0209': '撤销',
    '0301': '公开',
    '0302': '实质审查',
    '0401': '未确认',
  },
  // 经营异常原因类型整理
  exceptionType: {
    1: '登记的住所/经营场所无法联系企业',
    2: '未按规定公示企业信息',
    3: '公示信息隐瞒真实情况/弄虚作假',
    4: '未在登记所从事经营活动',
    5: '未在规定期限公示年度报告',
    6: '商事主体名称不适宜',
    7: '其他',
  },
};

export const AdvancedConditions = {
  // tender.wtbnames  中标单位名称
  tdwtbns: 'tender.wtbnames',
  // tender.ifbnames 招标单位名称
  tdifbns: 'tender.ifbnames',
  // 招投标中标金额 万元
  tdwtba: 'tender.amount',
};

// 公司主表查询条件
export const CompanyFilterMap = {
  /** ---------------------------------------------------------- */
  /** --------------------------------- 主表数据条件 -------------- */
  id: '_id',
  // 经营状态
  sc: 'statuscode',
  // 所属地区
  r: {
    //   省
    pr: 'province',
    //  市/区县
    ac: 'areacode',
  },
  // 国民行业
  i: {
    ind: 'industry',
    sub: 'subind',
  },
  // 注册资本
  rca: 'registcapiamount',
  // 资本类型
  ru: 'registerunit',
  // 成立年限
  sd: 'startdatecode',
  // 企业类型
  ekc: 'econkindcode',
  // 组织机构
  ot: 't_type',
  // 参保人数
  ic: 'insuredcount',
  // 有无年报 Flag.YR  // 有无分支机构 Flag.B  // 是否一般纳税人 Flag.GT
  flag: 'flag',
  // 联系方式（有手机 Flag.MN， 有固话 Flag.T，有邮箱 Flag.E）
  ci: 'flag',
  // 融资阶段
  fl: 'financinglevel',
  // 上市板块  listingInfo的值
  // 1: '新三板',
  // // 2: 'A股', 已拆分成  21   主板   22   中小板  23  创业板
  // 21: '主板',
  // 22: '中小板',
  // 23: '创业板',
  // 6: '港股',
  // 7: '中概股',
  // 301: '新四板',
  // 501: '科创板'
  lst: 'listingstatus',
  // 新兴行业
  tag: 'tag',
  //高新企业
  gxqy: 'tag',
  // 最新纳税信用等级
  ltl: 'latesttaxlevel',
  // 最新招聘日期
  lrd: 'latestrecruitmentdate',
  // 岗位名称 (最近1个月招聘岗位名称)
  pos: 'position',
  // 当前招聘人数
  plc: 'personnelcount',
  // 招聘岗位总数
  pnc: 'positionscount',
  //   // 企业码
  //   qcccode: 'qcccode',
  // 企业名称
  name: 'name',
  // originalname 曾用名
  originalname: 'originalname',
  // 企业简介
  intr: 'introduction',
  // 经营范围
  scope: 'scope',
  // 注册地址  address
  address: 'address',
  // 年报地址  address2
  address2: 'address2',
  // 纳税人资格有效期
  tpd: 'taxpayerdate',
  // 变更日期
  cd: 'changedate',
  // 变更类型
  ct: 'changetype',
  // 最新变更日期
  lcd: 'latestchangedate',
  //变更日期
  ud: 'updatedate',
  // 最新变更类型
  lct: 'latestchangetype',
  // 手机来源渠道
  ps: 'phonesource',
  // 固话来源渠道
  ts: 'telsource',
  // 邮箱来源渠道
  es: 'emailsource',
  // 主营产品
  pro: 'product',
  // // 主营业务
  // bus: 'business',
  // 最新纳税信用评价年度
  lty: 'latesttaxyear',
  // 融资次数
  fr: 'financinground',
  // 最新融资日期
  lfd: 'latestfinancingdate',
  // 最新融资金额
  lfa: 'latestfinancingamount',
  // 上市日期
  ld: 'listdate',
  // 行政许可数量
  adlc: 'adlicensecount',
  // 经营异常数量
  mec: 'manageexceptioncount',
  // 行政处罚数量
  pc: 'punishcount',
  // 有效状态商标数量
  vpc: 'validpatentcount',
  // 无效状态商标数量
  ipc: 'invalidpatentcount',
  // invalidtrademarkaddress 商标地址待变更 (1-有、0-无)
  ipa: 'invalidtrademarkaddress',
  // // 最近一年申请专利数量
  // lyapc: 'lyapcpatentcount',
  // 最近一年公开专利数量
  lyppc: 'lypubpatentcount',
  // 证书数量
  cretc: 'cretcount',
  // 本年度获证数量
  tcretc: 'tcretcount',
  // 作品著作权数量
  coprc: 'coprcount',
  // 软件著作权数量
  scoprc: 'scoprcount',
  // ICP备案数量
  icpc: 'icpcount',
  //   最新地块公示日期		lastpublicitydate
  lpbd: 'lastpublicitydate',
  // 最新购地合同签订日期		lastpurchasedate
  lpcd: 'lastpurchasedate',

  // 最新土地转让成交日期		lastmarketdate
  lmd: 'lastmarketdate',
  //   // 最新对外投资日期		lastinvestmentdate
  // lid: 'lastinvestmentdate',
  //   // 最新产权交易起始日期		lastproeprtydate
  //   lprd: 'lastproeprtydate',

  // 最新行政许可日期		lastlicensedate
  lld: 'lastlicensedate',
  // 最新列入经营异常日期		lastexceptionsdate
  led: 'lastexceptionsdate',
  // 最新移出经营异常日期		lastremoveexceptionsdate
  lred: 'lastremoveexceptionsdate',
  // 最新行政处罚决定日期		lastpunishmentdate
  lpmd: 'lastpunishmentdate',
  // 最新环保处罚日期		lastenvpubdate
  lend: 'lastenvpubdate',
  // 最新税收违法发布日期		lastmajortaxillegaldate
  lmtd: 'lastmajortaxillegaldate',
  // 最新动产抵押日期		lastmpledgedate
  lmpd: 'lastmpledgedate',
  // 最新司法拍卖时间		lastauctionsdate
  lad: 'lastauctionsdate',
  // 最新询价评估发布日期		lastevaluationdate
  levd: 'lastevaluationdate',
  // 最新土地抵押截止日期		lastmortgagedate
  lmgd: 'lastmortgagedate',
  // 最新公示催告公告日期		lastpublicnoticedate
  lpnd: 'lastpublicnoticedate',
  // 最新欠税公告发布日期		lasttaxowedate
  ltod: 'lasttaxowedate',
  // 最新被执行人立案日期		lastzxdate
  lzd: 'lastzxdate',
  // 最新失信发生日期		lastsxdate
  lxd: 'lastsxdate',
  // 最新终本案件立案日期		lastendexecutioncasedate
  leecd: 'lastendexecutioncasedate',
  // 最新限制高消费发布日期		lastsumptuarydate
  lsd: 'lastsumptuarydate',

  // 最新刊登日期		lastchinacourtdate
  lchd: 'lastchinacourtdate',
  // 最新开庭公告开庭时间		lastcourtnoticedate
  lcod: 'lastcourtnoticedate',
  // 最新诉讼日期		lastregisterdate
  lrgd: 'lastregisterdate',

  // 最新裁判文书发布日期		lastcasedate
  lcsd: 'lastcasedate',

  // 初次获证日期		oldcertificatedate
  ocd: 'oldcertificatedate',
  // 最新获证日期		lastcertificatedate
  lccd: 'lastcertificatedate',

  // 网站名称 网址		website
  ws: 'website',
  // 网站关键词
  wsinfo: 'websitetitleinfos',
  // b2b商品名称
  b2bp: 'b2bproduct',
  // b2b商品行业分类
  b2bpc: 'b2bproductcategory',

  // 总融资金额	allfinancingamount
  afa: 'allfinancingamount',

  // 最新信用评级日期		lastratingdate
  lrad: 'lastratingdate',
  // 对外投资金额		investmentamount
  ia: 'investmentamount',

  //   最新列入严重违法日期		lastseriousviolationsdate
  lsvad: 'lastseriousviolationsdate',
  //   最新股权出质登记日期		lastequitydate
  leqd: 'lastequitydate',
  //   最新股权质押公告日期		lastpledgedate
  lpld: 'lastpledgedate',

  //   破产重整公开日期		lastbankruptcydate
  lbrd: 'lastbankruptcydate',

  //   最新土地抵押起始日期		lastmortgagestartdate
  lmord: 'lastmortgagestartdate',

  //   最新刊登日期		lastchinacourtdate
  lasccd: 'lastchinacourtdate',

  // 专利数量	 patentcount
  pac: 'patentcount',
  //   最新立案日期		lastliandate
  lasld: 'lastliandate',

  // //   最近3个月内截止证书类别		3crettype (删除)
  // crt3: '3crettype',
  // //   最近6个月内截止证书类别		6crettype (删除)
  // crt6: '6crettype',

  //  企业潜力
  // HT001: '高新技术企业',
  // UE: '独角兽企业',
  // GE: '瞪羚企业',
  // YEE: '雏鹰企业',
  // ITE: '创新型科技企业',
  // STE: '科技小巨人企业',
  // SSE: '专精特新企业',
  // T_TSMES: '科技型中小企业',
  // T_PT: '民营科技企业',
  // T_TC: '企业技术中心',
  // T_MS: '国家备案众创空间',
  // T_CI: '科技企业孵化器',
  // T_TD: '技术创新示范企业',
  // T_IC: '隐形冠军企业',
  // T_ATS: '技术先进型服务企业',
  // T_AC: '牛羚企业',
  // XWQY: '小微企业',
  qxql: 'flag',
  //企查查认证等级
  qccl: 'qccauthenticationlevel',
  // 实缴资本金额数字(万元)
  rcpa: 'reccapamount',
  // 上市企业年度营业额(万元)
  yysra: 'yysramount',
  // csaiiistatus 两化状态 0未接触,1接触,2启动,3完成
  csaiiis: 'csaiiistatus',
  // csaiiibegindate 两化证书开始日期
  csaiiibd: 'csaiiibegindate',
  //  csaiiienddate 两化证书结束日期
  csaiiied: 'csaiiienddate',
  // 法院名称
  courtnames: 'courtnames',
  // // 经营异常原因（已删除，放到子表了）
  // reasons: 'reasons'
  // 最新年报年份
  reporty: 'reportyear',
  // billingagenttype 疑似代记账号码
  bat: 'billingagenttype',

  pminc: 'peoplemincount',

  // 人员规模
  peoplec: {
    // peoplemincount 人员规模最小值
    min: 'peoplemincount',
    // peoplemaxcount 人员规模最大值
    max: 'peoplemaxcount',
  },
  // 信用评级 comratinglevel
  coml: 'comratinglevel',
  //   cancellationdate 注销日期
  cld: 'cancellationdate',
  // revocationdate 吊销日期
  rcd: 'revocationdate',
  // taxpayertypes 纳税人资质
  tpt: 'taxpayertypes',
  // iprightpledgecount 知识产权出质总数
  iprpc: 'iprightpledgecount',
  // iprightpledgenewdate 最新知识产权出质公告日期
  iprpd: 'iprightpledgenewdate',
  //clickcount 企业浏览量
  ckc: 'clickcount',
  //overseapatentcount 国际专利数量
  ospc: 'overseapatentcount',
  //franchisecount 商业特许经营数量
  fct: 'franchisecount',

  // agentcomcount---合作企业数量
  // agentcoopcount---合作总次数
  // proprietorshipregion 独资企业地区 (美国、法国、德国、韩国、日本、香港、台湾、澳门)
  ppsr: 'proprietorshipregion',

  //revenuehierarchy 营收水平万元
  zjrhc: 'revenuehierarchy',
  //profithierarchy 利润水平万元
  zjphc: 'profithierarchy',
  //assetshierarchy 资产规模
  zjasc: 'assetshierarchy',
  // personlist-姓名名单
  psl: 'personlist',
};

// 招投标 子表数据条件
export const TenderFilterMap = {
  // 招投标项目名称		tender.title
  tt: 'tender.title',
  // 招投标项目角色	招标单位、投标单位、代理单位	tender.role
  tr: 'tender.role',
  // 招投标发布时间		tender.publishdate
  tdpd: 'tender.publishdate',
  // 开标时间		tender.opendate
  tdod: 'tender.opendate',
  // 招投标项目预算		tender.budgetvalue
  tdbv: 'tender.budgetvalue',
  // tender.ifbprogress
  // 招投标项目阶段	招标、投标、开标、评标、中标
  // 招投标项目结果	成功、失败（流标/废标/终止）
  tdifbp: 'tender.ifbprogress',
  // 招投标项目所属行业	市政设施、行政办公、能源化工、服务采购、政府采购、药品采购、建筑工程、医疗卫生、交通工程、水利水电、弱点安防、信息技术、其他
  tdi: 'tender.industry',
  // 招投标项目地区	精确到市	tender.city/tender.province
  tdr: {
    //   省
    pr: 'tender.province',
    //  城市
    ac: 'tender.city',
  },
  // tender.wtbinfo 中标单位json
  tdwtbif: 'tender.wtbinfo',
  // tender.ifbinfo 招标单位json
  tdifbif: 'tender.ifbinfo',
  // tender.agentnames 代理单位名称
  tdans: 'tender.agentnames',
  // tender.agentinfo 代理单位json
  tdaif: 'tender.agentinfo',
  // 招投标项目结果
  tdifbpr: 'tender.ifbprogress',

  ...AdvancedConditions,

  // // 有无拟建项目	Y-有拟建项目、N-无拟建项目
  // tdnj: 'tender.ifbprogress',
  // // 拟建项目名称		tender.title
  // tdnjt: 'tender.title',
  // // 拟建项目发布时间		tender.publishdate
  // tdnjpd: 'tender.publishdate',
  // // 招投标行业  tender.industry
  // tdnji: 'tender.industry',
  // // 拟建项目地区 tender.city/tender.province
  // tdnjr: {
  //   pr: '省code',
  //   ac: '市code'
  // },
  // // 拟建项目批复结果 'tender.ifbprogress'
  // tdnjifbpr: 'tender.ifbprogress'
};

// 工商变更 子表数据条件
export const CompanyChangeFilterMap = {
  // companychange.changedate  '变更日期',
  cccd: 'companychange.changedate',
  // companychange.projectname '变更类型',
  ccpn: 'companychange.projectname',
  // '浮动类型 0:不变 1:下降 2:上升',
  ccfct: 'companychange.floatchangetype',
  //   '浮动百分比',
  ccfpt: 'companychange.floatpercentage',
  //  '浮动绝对值(万元人民币)',
  ccfabs: 'companychange.floatabs',

  // companychange.category （字段定义为双类型）---变更类型
  cccg: 'companychange.category',
  // companychange.scopebeforeinfos---经营范围增项
  ccsbi: 'companychange.scopebeforeinfos',
  // companychange.scopeafterinfos---经营范围减项
  ccafi: 'companychange.scopeafterinfos',
};

// 司法案件 子表数据条件
export const CompanyCaseFilterMap = {
  // companycase.casetype 司法案件类型
  ccct: 'companycase.casetype',
  // companycase.roletype 司法案件身份
  ccrt: 'companycase.roletype',
  // companycase.latesttrialround 司法案件进程
  cclsl: 'companycase.latesttrialround',
  // companycase.casereason 司法案件案由
  cccre: 'companycase.casereason',
};

// 经营异常 子表数据条件
export const ExceptionFilterMap = {
  // 经营异常原因数据整理		exception.reason
  er: 'exception.reason',
  // 经营异常原因 类型数据整理
  ert: 'exception.type',
  // 列入经营异常名录原因		exception.addreason
  ea: 'exception.addreason',
  //   列入经营异常日期		exception.adddate
  ead: 'exception.adddate',
  //   移出经营异常日期		exception.removedate
  erd: 'exception.removedate',
  //   移出经营异常名录原因		exception.romovereason
  err: 'exception.romovereason',
};

// 证书 子表数据条件
export const CretFilterMap = {
  // 涵盖证书类别		cret.type
  crt: 'cret.type',
  // 证书类型三级分类		cret.name
  crtn: 'cret.name',
  // 证书类型一级分类		cret.level1name
  crtl1n: 'cret.level1name',
  // 证书类型二级分类		cret.level2name
  crtl2n: 'cret.level2name',
  // 证书名称		cret.primaryproductname
  crn: 'cret.primaryproductname',
  // 证书发证日期		cret.startdate
  crs: 'cret.startdate',
  // 证书有效截止日期		cret.enddate
  ced: 'cret.enddate',
  // 资质证书最新截止日期		cret.latestdate
  cltd: 'cret.latestdate',
  // 证书编号 "cret.no", 分词字段 "cret.nos"
  cretn: 'cret.nos.text',
  // 证书颁发机构 "cret.beloneorg"
  cretbo: 'cret.beloneorg.text',
  // 证书状态  cret.status  0其他、1有效、2过期、3失效、4撤销、5变更、6暂停、7正常、8注销等
  crets: 'cret.status',
};

// 立案 子表数据条件
export const RegisterFilterMap = {
  //   立案日期		register.riskdate
  rgsrd: 'register.riskdate',
  //   立案当事人 register.type	公诉人、原告、上诉人、申请人、被告人、被告、被上诉人、被申请人
  rgst: 'register.type',
  // 立案法院名称 "register.courtname"
  rgscn: 'register.courtname',
  // 案件号  "register.caseno"
  rgsn: 'register.caseno',
  // 案件原因 "register.casereason"
  rescr: 'register.casereason',
};

// 招聘 子表数据条件
export const RecruitmentFilterMap = {
  // 岗位名称
  rect: 'recruitment.title',
  // 岗位描述
  red: 'recruitment.description',
  // 招聘渠道
  res: 'recruitment.source',
  // 岗位薪酬（月薪）		recruitment.salaryvalue
  recs: 'recruitment.salaryvalue',
  // 岗位学历	，本科、专科、研究生、高中、初中、小学	recruitment.edugroupcode
  recegc: 'recruitment.edugroupcode',
  // 岗位经验		recruitment.expgroupcode
  recexgc: 'recruitment.expgroupcode',
  // 岗位发布时间		recruitment.publishdate
  recpd: 'recruitment.publishdate',
  // 招聘地址
  recr: {
    // 招聘地址省		recruitment.province
    pr: 'recruitment.province',
    // 招聘地址区县  recruitment.areacode
    ac: 'recruitment.areacode',
  },
};

// 开庭公告 子表数据条件
export const CourtnoticeFilterMap = {
  //   开庭公告开庭时间		courtnotice.liandate
  cnld: 'courtnotice.liandate',
  //   开庭公告案由		courtnotice.casereason
  cncr: 'courtnotice.casereason',
  //   开庭公告当事人 courtnotice.type	公诉人、原告、上诉人、申请人、被告人、被告、被上诉人、被申请人
  cnt: 'courtnotice.type',
  // courtnotice.executegov： 开庭公告的法院名称
  cneg: 'courtnotice.executegov',
};

// 终本案件 子表数据条件
export const EndexecutioncaseFilterMap = {
  //   终本案件立案日期		endexecutioncase.judgedate
  eecjd: 'endexecutioncase.judgedate',
  //   终本案件终本日期		endexecutioncase.enddate
  eeced: 'endexecutioncase.enddate',
  //   终本案件未履行金额		endexecutioncase.failureact
  eedfa: 'endexecutioncase.failureact',
  //   终本案件原告 'endexecutioncase.prosecutor'
  eecpc: 'endexecutioncase.prosecutor.text',
  //   终本案件被告 'endexecutioncase.defendant'
  eecda: 'endexecutioncase.defendant.text',
};

// 专利 子表数据条件
export const PatentFilterMap = {
  // 专利类型	发明专利、实用新型专利、外观设计专利	patent.kindcode
  pkc: 'patent.kindcode',
  // 专利名称		patent.title
  pt: 'patent.title',
  //   专利公开（公告）日期		patent.pubdate
  ppk: 'patent.pubdate',
  // 法律状态
  pts: 'patent.status',
  // 专利代理机构 patent.agency
  pa: 'patent.agency',
};

// 专利数量统计 按类型统计子表
export const PatentTypeFilterMap = {
  // patenttype.fmgb  发明公布 专利数量统计
  ptfmgb: 'patenttype.fmgb',
  // patenttype.fmsq  发明授权
  ptfmsq: 'patenttype.fmsq',
  // patenttype.syxx  实用新型
  ptsyxx: 'patenttype.syxx',
  // patenttype.wgsj  外观设计
  ptwgsj: 'patenttype.wgsj',
};

// 专利数量统计 按年份统计子表
export const PatentYearFilterMap = {
  // atentyear.year  专利年份
  ptyear: 'patenttype.year',
  // patentyear.cnt  专利数量
  ptcnt: 'patenttype.cnt',
};

//国际专利
export const OverseaPatentFilterMap = {
  // 专利名称
  ospt: 'overseapatent.title',
  // 专利申请日期
  ospad: 'overseapatent.applicationdate',
  //   专利公开（公告）日期
  osppid: 'overseapatent.publicationdate',
  // 发明人
  ospi: 'overseapatent.inventors',
  // 代理机构
  opa: 'overseapatent.agency',
};

// 商业特许经营 维度，即连锁店维度
export const FranchisePatentFilterMap = {
  // 特许人名称
  fifn: 'franchise.franchisorname',
  // 备案公示日期
  fird: 'franchise.recorddate',
};

// 代理机构(知识产权行业)合作企业子维度
export const CooperationFilterMap = {
  // 合作企业keyno
  cckn: 'cooperation.companykeyno',
  // 合作企业名称
  ccn: 'cooperation.companyname',
  // 合作类型
  ccot: 'cooperation.cooptype',
  // 合作次数
  ccpc: 'cooperation.coopcount',
  // "cooperation.agentkeyno"   代理机构keyno
  ccpak: 'cooperation.agentkeyno',
  // "cooperation.agentname"  代理机构名称
  ccpan: 'cooperation.agentname',
};

// 商标文书 trademarkdetail
export const TrademarkDetailFilterMap = {
  // trademarkdetail.title---商标文书标题
  tadt: 'trademarkdetail.title',
  // trademarkdetail.judgeno---商标文书裁定/决定文书号
  tadj: 'trademarkdetail.judgeno',
  // trademarkdetail.applyname---商标文书申请人
  tadan: 'trademarkdetail.applyname.text',
  // trademarkdetail.reapplyname---商标文书被申请人
  tadran: 'trademarkdetail.reapplyname.text',
  // trademarkdetail.proxyname---商标文书代理人
  tadpn: 'trademarkdetail.proxyname.text',
  // trademarkdetail.reproxyname---商标文书代理人(被申请)
  tadrpn: 'trademarkdetail.reproxyname.text',
  // trademarkdetail.publishdate---商标文书公布日期
  tadpd: 'trademarkdetail.publishdate',
  // trademarkdetail.osskey---商标文书展示的osskey
  tadok: 'trademarkdetail.osskey',
  // trademarkdetail.applykeynos---商标文书申请人keynos
  tadakn: 'trademarkdetail.applykeynos',
  // trademarkdetail.reapplykeynos---商标文书被申请人keynos
  tadrapkn: 'trademarkdetail.reapplykeynos',
  // trademarkdetail.proxykeynos---商标文书代理人keynos
  tadpkn: 'trademarkdetail.proxykeynos',
  // trademarkdetail.reproxykeynos---商标文书被代理人keynos
  tadrpkn: 'trademarkdetail.reproxykeynos',
  // trademarkdetail.tmtype---商标文书的类型  商标异议决定书 商标注册审查决定书(未使用)  商标裁定书
  tadtt: 'trademarkdetail.tmtype',
  // trademarkdetail.tmtypecode---商标文书的类型code
  // 3-特殊商标;
  // 6-证明商标;
  // 7-普通商标;
  // 11-集体商标;
  tadttc: 'trademarkdetail.tmtypecode',
};

/**
 * 搜索字段映射
 */
export const KysFilterMap = {
  termsFlag: 'flag',
  // 地图查询
  map: {
    // 圆形范围查询
    distance: 'distance',
    // 不规则图形范围查询
    polygon: 'polygon',
  },
  // 主表条件
  ...CompanyFilterMap,
  // 招投标 子表数据条件
  ...TenderFilterMap,

  // 工商变更 子表数据条件
  ...CompanyChangeFilterMap,

  // 司法案件 子表数据条件
  ...CompanyCaseFilterMap,

  // 经营异常 子表数据条件
  ...ExceptionFilterMap,

  // 证书 子表数据条件
  ...CretFilterMap,

  // 立案 子表数据条件
  ...RegisterFilterMap,

  // 招聘 子表数据条件
  ...RecruitmentFilterMap,

  // 开庭公告 子表数据条件
  ...CourtnoticeFilterMap,

  // 终本案件 子表数据条件
  ...EndexecutioncaseFilterMap,

  //  专利 子表数据条件
  ...PatentFilterMap,

  // 专利数量统计 按类型统计子表
  ...PatentTypeFilterMap,

  // 国际专利
  ...OverseaPatentFilterMap,

  //商业特许经营维度，即连锁店维度
  ...FranchisePatentFilterMap,

  // 代理机构(知识产权行业)合作企业子维度
  ...CooperationFilterMap,

  // 商标文书 trademarkdetail
  ...TrademarkDetailFilterMap,

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 海关 子表数据条件 -------------- */
  // 进出口信用注册日期		customs.regdate
  cr: 'customs.regdate',
  // 海关登记经营类别		customs.tradetype
  ctt: 'customs.tradetype',
  //  海关信用类型	customs.reggov
  // crg: 'customs.reggov',
  // 海关登记行业	customs.industrytype
  cit: 'customs.industrytype',
  // 进出口信用状态	customs.cancellationflag
  ccf: 'customs.cancellationflag',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 购地信息 子表数据条件 -------------- */

  // 购地合同签订日期		purchase.signcontracttime
  blsct: 'purchase.signcontracttime',
  // 购地信息土地用途		purchase.landuse
  bllu: 'purchase.landuse',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 行政许可 子表数据条件 -------------- */

  // 行政许可文件名称		license.name
  ln: 'license.name',
  // 行政许可内容		license.content
  lc: 'license.content',
  // 行政许可有效期	有效期自与有效期至任何一个值在所选择的区间即满足展示条件	license.liandate
  licld: 'license.liandate',
  //   行政许可机关		license.executegov
  liceg: 'license.executegov',
  //   行政许可内容		license.content
  licrossFields: 'license.content',
  //   行政许可状态		license.lawsuitresult2
  liclr: 'license.lawsuitresult2',

  /** ---------------------------------------------------------------- */

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 裁判文书 子表数据条件 -------------- */
  // 裁判文书案由		case.casereason
  crossFields: 'case.casereason',
  //   裁判文书标题		case.name
  casen: 'case.name',
  //   裁判文书发布日期		case.submitdate
  casesd: 'case.submitdate',
  //   裁判文书案件身份	 case.type(原告/被告)
  caset: 'case.type',
  //   裁判文书案件金额		case.amountinvolved
  caseai: 'case.amountinvolved',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 法院公告 子表数据条件 -------------- */
  // 法院公告类型		chinacourt.categorycode
  ccrossFields: 'chinacourt.categorycode',
  //   法院公告当事人 chinacourt.type	公诉人、原告、上诉人、申请人、被告人、被告、被上诉人、被申请人
  cct: 'chinacourt.type',
  //   法院公告案由		chinacourt.casereason
  cccr: 'chinacourt.casereason',
  //   刊登日期		chinacourt.publishdate
  ccpd: 'chinacourt.publishdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 商标 子表数据条件 -------------- */
  // 商标名		trademark.name
  tn: 'trademark.name',
  // 商标专用权结束日期		trademark.validperiod
  tv: 'trademark.validperiod',
  //   商标流程状态  trademark.flowstatus	商标被驳回、商标进行形式审查、商标进行实质审查、商标驳回复审通过、商标注册存在异议、商标公告、商标注册成功
  tfs: 'trademark.flowstatus',
  //   商标申请日期		trademark.appdate
  tad: 'trademark.appdate',
  //   商标国际分类		trademark.intcls
  ti: 'trademark.intcls',
  //   商标内容		trademark.similargroups
  tsg: 'trademark.similargroups',
  //   商标代理机构		trademark.agency
  tra: 'trademark.agency',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 作品 子表数据条件 -------------- */
  // 作品名称		copr.name
  cn: 'copr.name',
  //   作品首次发表日期		copr.publishdate
  copd: 'copr.publishdate',
  //   作品创作完成日期		copr.finishdate
  cofd: 'copr.finishdate',
  //   作品登记日期		copr.registerdate
  cord: 'copr.registerdate',
  //   作品登记类别  copr.type	文字作品、音乐作品、戏剧作品、曲艺作品、舞蹈作品、美术作品、摄影作品、电影作品、电视（录像）作品、“工程设计、产品设计图纸及其说明”、“地图、示意图等图形作品”、“法律、行政法规规定的其他作品”、录音、录像
  cot: 'copr.type',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 软件著作权 子表数据条件 -------------- */
  // 软件名称		scopr.name
  scn: 'scopr.name',
  //   软件著作权登记日期		scopr.registerapprdate
  corad: 'scopr.registerapprdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 地块公示 子表数据条件 -------------- */
  // 地块公示发布日期		publicity.publishdate
  ppd: 'publicity.publishdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 土地转让 子表数据条件 -------------- */
  // 土地转让成交日期		deal.transactiontime
  dtt: 'deal.transactiontime',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 信用评级 子表数据条件 -------------- */
  // 信用评级机构		rating.orgname
  ron: 'rating.orgname',
  // 信用评级日期		rating.ratingdate
  rrd: 'rating.ratingdate',
  // 信用评级级别		rating.comratinglevel
  rcl: 'rating.comratinglevel',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 产权交易 子表数据条件 -------------- */
  // 产权交易标的名称		transaction.targetname
  ttn: 'transaction.targetname',
  // 产权交易价格		transaction.transferreserveprice
  ttrp: 'transaction.transferreserveprice',
  // 产权交易起始日期		transaction.startdate
  tsd: 'transaction.startdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 电信许可 子表数据条件 -------------- */
  //   电信许可业务范围		telicense.scope
  tls: 'telicense.scope',
  //   电信许可状态	有效、无效	telicense.isok
  tli: 'telicense.isok',

  /** ---------------------------------------------------------------- */
  /** ---------------------------------  列入严重违法 子表数据条件 -------------- */
  //   列入严重违法日期		seriousviolations.adddate
  svad: 'seriousviolations.adddate',
  //   列入严重违法企业名单原因		seriousviolations.addreason
  svar: 'seriousviolations.addreason',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 股权出质 子表数据条件 -------------- */
  //   股权出质状态	有效、无效	equity.status
  eqs: 'equity.status',
  //   出质股权数额（万元）		equity.pledgedamount
  eqpa: 'equity.pledgedamount',
  //   股权出质登记日期		equity.regdate
  eqrd: 'equity.regdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 股权质押 子表数据条件 -------------- */
  //   股权质押原因		pledge.frozenreason
  plfr: 'pledge.frozenreason',
  //   股权质押目的		pledge.pledgepur
  plpp: 'pledge.pledgepur',
  //   股权质押开始日期		pledge.startdate
  plsd: 'pledge.startdate',
  //   股权质押解除日期		pledge.enddate
  pled: 'pledge.enddate',
  //   股权质押状态	未达预警线、已解除质押	pledge.type
  plt: 'pledge.type',
  //   股权质押公告日期		pledge.noticedate
  plnd: 'pledge.noticedate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 行政处罚 子表数据条件 -------------- */
  //   行政处罚类型	 punish.punish_reason_type
  //   警告、罚款、“没收违法所得、没收非法财物”、责令停产停业、“暂扣或者吊销许可证、暂扣或者吊销执照”、行政拘留、“法律、行政法规规定的其他行政处罚”
  punprt: 'punish.punish_reason_type',
  //   行政处罚决定日期		punish.punish_date
  punpd: 'punish.punish_date',
  //   行政处罚事由		punish.punish_reason
  punpr: 'punish.punish_reason',
  //   行政处罚结果		punish.punish_result
  punpre: 'punish.punish_result',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 环保处罚 子表数据条件 -------------- */
  //   环保处罚事由		environmental.punishreason
  envpunr: 'environmental.punishreason',
  //   环保违法类型  environmental.illegaltype	违反环境影响评价类、违反“三同时”制度类、违反排污申报登记制度类、违反排污许可证制度类、违反排污费征收使用管理制度类、违反限期治理制度类、违反现场检查制度类、违反环境污染与破坏事故的报告及处理制度类
  envit: 'environmental.illegaltype',
  //   环保处罚依据		environmental.punishbasis
  envpb: 'environmental.punishbasis',
  //   环保处罚结果		environmental.punishmentresult
  envpr: 'environmental.punishmentresult',
  //   环保处罚日期		environmental.punishdate
  envpd: 'environmental.punishdate',
  //   环保处罚执行情况		environmental.implementation
  envi: 'environmental.implementation',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 税收违法 子表数据条件 -------------- */
  //   税收违法发布日期		tax.publishtime
  taxpt: 'tax.publishtime',
  //   税收违法案件性质		tax.casenature
  taxcn: 'tax.casenature',
  //   税收违法事实及处罚情况		tax.illegalcontent
  taxic: 'tax.illegalcontent',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 动产抵押 子表数据条件 -------------- */
  //   动产抵押状态		chattel.status
  chs: 'chattel.status',
  //   动产抵押登记日期		chattel.registerdate
  chrd: 'chattel.registerdate',
  //   动产抵押开始日期
  chsd: 'chattel.startdate',
  //   动产抵押结束日期
  ched: 'chattel.enddate',
  //   动产抵押债权金额
  cha: 'chattel.amount',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 司法拍卖 子表数据条件 -------------- */
  //   司法拍卖标题		judicial.name
  jun: 'judicial.name',
  //   司法拍卖时间		judicial.actionremark
  juam: 'judicial.actionremark',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 询价评估 子表数据条件 -------------- */
  //   询价评估标的物		evaluation.target
  evat: 'evaluation.target',
  //   询价评估发布日期		evaluation.publicdate2
  evp: 'evaluation.publicdate2',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 土地抵押 子表数据条件 -------------- */
  //   土地抵押起始日期		mortgage.starttime
  morsd: 'mortgage.starttime',
  //   土地抵押截止日期		mortgage.endtime
  mored: 'mortgage.endtime',
  //   土地抵押面积（公顷）		mortgage.mortgageacreage
  morma: 'mortgage.mortgageacreage',
  //   土地抵押金额（万元）		mortgage.mortgageprice
  mormp: 'mortgage.mortgageprice',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 公示催告 子表数据条件 -------------- */
  //   公示催告票面金额		publicnotic.pmmoneyint
  pubp: 'publicnotic.pmmoneyint',
  //   公示催告票据类型 publicnotic.pjtype	汇票、本票、支票
  pubpt: 'publicnotic.pjtype',
  //   公示催告公告日期		publicnotic.publishdt
  pubpd: 'publicnotic.publishdt',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 欠税 子表数据条件 -------------- */

  //   欠税税种  owe.title 	增值税、消费税、企业所得税、个人所得税、资源税、城市维护建设税、房产税、印花税、城镇土地使用税、土地增值税、车船使用税、船舶吨税、车辆购置税、关税、耕地占用税、契税、烟叶税、环保税。只有个人所得税、企业所得税、车船税、环保税、烟叶税、船舶吨位税
  owet: 'owe.title',
  //   欠税余额（元）		owe.amt
  owea: 'owe.amt',
  //   最新发生的欠税金额（元）		owe.newamt
  owen: 'owe.newamt',
  //   欠税公告发布日期		owe.publishdate
  owepd: 'owe.publishdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 被执行人 子表数据条件 -------------- */
  //   被执行人立案日期		zhixing.liandate
  zxld: 'zhixing.liandate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 失信被执行人 子表数据条件 -------------- */
  //   失信被执行人立案日期		shixin.liandate
  sxld: 'shixin.liandate',
  //   失信被执行人发布日期		shixin.publicdate
  sxpd: 'shixin.publicdate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 限制高消费 子表数据条件 -------------- */
  //   限制高消费发布日期		sumptuary.publishdate
  suppd: 'sumptuary.publishdate',
  //   限制高消费立案日期	 sumptuary.judgedate
  jd: 'sumptuary.judgedate',

  /** ---------------------------------------------------------------- */
  /** --------------------------------- 股权冻结 子表数据条件 -------------- */
  //   股权冻结数量		assistance.equityamount
  // asea: 'assistance.equityamount',
  //   股权冻结状态  assistance.status  股权冻结|冻结，股权冻结|解除冻结
  ass: 'assistance.status',
};
