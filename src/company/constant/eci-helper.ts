export enum HitReasonFieldCollection {
  Name = '公司名称',
  RegNo = '注册号',
  OrgNo = '组织机构代码',
  CreditCode = '统一社会信用代码',
  ShortName = '简称',
  EnglishName = '英文名称',
  OriginalName = '曾用名',
  BelongOrg = '登记机关',
  Address = '公司地址',
  ARAddress = '最新年报地址',
  Product = '品牌/产品',
  Employee = '主要人员',
  Scope = '经营范围',
  Invester = '投资人',
  Wechat = '微信公众号',
  Trademark = '商标',
  patentlist = '专利',
  Contact = '联系方式',
  Email = '电子邮件',
  Copyright = '著作权',
  SoftwareCopyright = '软件著作权',
  Website = '网址',
  WebsiteName = '网站名称',
  Tag = '标签',
  Distance = '坐标',
  StockCode = '股票代码',
  StockName = '股票简称',
  Oper = '法定代表人',
  CEO = '董事长',
  Operator = '经营者',
  Charger = '负责人',
  ExecutivePartner = '执行事务合伙人',
  BoardDirector = '理事长',
  Representative = '代表人',
  HisPartner = '历史股东',
  Invest = '投资机构',
  TaxNo = '纳税人识别号',
  AliasName = '别名',
  // GroupOwner = '集团住公司',
  App = 'App',
  PinYin = '名称拼音',
  AliasName2 = '别称',
  BondCode = '债券代码',
  BondName = '债券简称',
  Default = '',
}
export interface HitReason {
  Field: HitReasonFieldCollection;
  Value: string;
}

export interface DefaultHitReasonEntity {
  Field: HitReasonFieldCollection;
  Value: string;
  FieldName: string;
  FieldValue: string[];
  DistinctValues: string[];
  DistinctValueLength: number;
  ExistingSameSearchKey: number;
}

export const GROUP_OPER_MAP = new Map([
  [1, HitReasonFieldCollection.Oper],
  [2, HitReasonFieldCollection.ExecutivePartner],
  [3, HitReasonFieldCollection.Charger],
  [4, HitReasonFieldCollection.Operator],
  [5, HitReasonFieldCollection.Invester],
  [6, HitReasonFieldCollection.CEO],
  [7, HitReasonFieldCollection.BoardDirector],
  [8, HitReasonFieldCollection.Representative],
]);

export const FIELD_NAME_REASON_MAP = new Map([
  ['regno', HitReasonFieldCollection.RegNo],
  ['orgno', HitReasonFieldCollection.OrgNo],
  ['creditcode', HitReasonFieldCollection.CreditCode],
  ['englishname', HitReasonFieldCollection.EnglishName],
  ['english_hk', HitReasonFieldCollection.EnglishName],
  ['belongorg', HitReasonFieldCollection.BelongOrg],
  ['employeelist', HitReasonFieldCollection.Employee],
  ['hispartner', HitReasonFieldCollection.HisPartner],
  // ['invest', HitReasonFieldCollection.Invest],
  ['taxno', HitReasonFieldCollection.TaxNo],
  ['opername', HitReasonFieldCollection.Oper],
  ['tag', HitReasonFieldCollection.Tag],
  ['name_alias', HitReasonFieldCollection.AliasName2],
]);

export const FIELD_NAME_REASON_MANUAL_MAP = new Map([
  ['product', HitReasonFieldCollection.Product],
  ['patentlist', HitReasonFieldCollection.patentlist],
  ['contactnumber', HitReasonFieldCollection.Contact],
  ['wechat', HitReasonFieldCollection.Wechat],
  ['email', HitReasonFieldCollection.Email],
  ['app', HitReasonFieldCollection.App],
  ['ambiguity', HitReasonFieldCollection.ShortName],
  ['invest', HitReasonFieldCollection.Invest],
]);

export const SPLITTED_CHARACTER_MAP = new Map([
  ['website', '。'],
  ['featurelist2', '。'],
  ['featurelist', '。'],
  ['product', '。'],
  ['patentlist', '。'],
  ['contactnumber', '。'],
  ['ambiguity', '。'],
  ['wechat', '。'],
  ['email', '。'],
  ['app', '。'],
  ['invest', '。'],
]);

export const REG_EXP_EM_PAIR = /<em>[^<]*<\/em>/gi;
export const REG_EXP_EM_SPECIAL_PAIR = /<em>[^(em)]*<\/em>/gi;
export const REG_EXP_EM_TAG = /<[^>]+>/gi;
export const REG_EXP_EM_INNER = /<\/em><em>/gi;
export const REG_EXP_CHARACTER_FULLSTOPPAIR = /<em>[。]<\/em>/gi;
export const REG_EXP_CHARACTER_SEMICOLONPAIR = /<em>[；;]<\/em>/gi;
export const REG_EXP_CHARACTER_COMMAPAIR = /<em>[，]<\/em>/gi;
export const REG_EXP_CHARACTER_SEMICOLONEPAIR = /<em>[;]<\/em>/gi;
export const REG_EXP_CHINESE_CHARACTER = /[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi;
