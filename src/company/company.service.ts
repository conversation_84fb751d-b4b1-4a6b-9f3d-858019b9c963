import { Logger } from 'log4js';
import { HttpException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '../config/config.service';
import { Client } from '@elastic/elasticsearch';
import { union } from 'lodash';
import { Cacheable, useNodeCacheAdapter } from 'type-cacheable';
import * as NodeCache from 'node-cache';
import { Contact, Email, Tel } from './model';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HttpUtilsService } from '../http/httputils.service';
import { ExactMatchCompanyRequest } from './model/ExactMatchCompanyRequest';
import { KysSearchExecutor } from './executor/KysSearchExecutor';
import { KysCompanyResponseDetails } from './model/KysCompanyResponseDetails';
import { KysCompanySearchRequest } from './model/KysCompanySearchRequest';
import { KysDefaultIncludeFields, KysSearchIndex } from './constant/constant.kys';
import { KysExactMatch } from './executor/KysExactMatch';

@Injectable()
export class CompanySearchService {
  private readonly logger: Logger = QccLogger.getLogger(CompanySearchService.name);
  private kysSearchExecutor: () => KysSearchExecutor;
  private kysExactMatch: () => KysExactMatch;

  constructor(private config: ConfigService, private readonly httpUtils: HttpUtilsService) {
    const kysClient = new Client({
      nodes: this.config.esConfig.kysCompany.nodes,
      headers: this.config.esConfig.kysCompany.headers,
      ssl: { rejectUnauthorized: false },
    });
    this.kysSearchExecutor = (): KysSearchExecutor => {
      return new KysSearchExecutor(kysClient, this.config.esConfig.kysCompany.indexName);
    };
    this.kysExactMatch = (): KysExactMatch => {
      return new KysExactMatch(kysClient, this.config.esConfig.kysCompany.indexName);
    };
    useNodeCacheAdapter(new NodeCache());
  }

  // @Cacheable({ ttlSeconds: 1800 })
  public async findByKys(companyId) {
    return this.kysSearchExecutor().get<KysCompanyResponseDetails>(companyId);
  }

  public async kysExactMatchSearch(searchBody: ExactMatchCompanyRequest): Promise<KysCompanyResponseDetails[]> {
    searchBody.includeFields = union(['id', 'name', 'creditcode', 'regno', 'status', 'statuscode', 'originalname'], searchBody.includeFields);
    searchBody.searchFields = searchBody?.searchFields?.length ? searchBody?.searchFields : ['name', 'taxno', 'creditcode', 'regno'];
    //  0: '大陆企业', 1: '社会组织', 11: '事业单位', 12: '律师事务所',
    // searchBody.searchType = searchBody?.searchType?.length ? searchBody?.searchType : ['0', '11', '12'];
    return this.kysExactMatch().exactMatch(searchBody);
  }

  public async kysSearch(searchBody: KysCompanySearchRequest) {
    try {
      searchBody.includeFields = union(KysDefaultIncludeFields, searchBody.includeFields);

      if (searchBody.searchKey && searchBody.searchKey.trim().length > 0) {
        //默认index
        if (!searchBody.searchIndex) {
          searchBody.searchIndex = KysSearchIndex;
        }
      }
      searchBody.sortOrder = searchBody.sortOrder || 'DESC';

      return this.kysSearchExecutor().search<KysCompanyResponseDetails>(searchBody);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(error);
        throw new InternalServerErrorException();
      }
    }
  }

  @Cacheable({ ttlSeconds: 300 })
  public async getContactByKys(companyId: string): Promise<Contact> {
    try {
      const details = await this.findByKys(companyId);
      const tellist: Tel[] = [];
      const emaillist: Email[] = [];
      for (let index = 0; index < details?.result?.tellistkzz?.length; index++) {
        const tel = details.result.tellistkzz[index];
        if (tel.t) {
          tellist.push(tel);
        }
      }

      for (let index = 0; index < details?.result?.emaillist?.length; index++) {
        const email = details.result.emaillist[index];
        if (email.e) {
          emaillist.push(email);
        }
      }

      return { tellist, emaillist };
    } catch (error) {
      this.logger.error(error);
      if (error instanceof HttpException) {
        throw error;
      } else {
        throw new InternalServerErrorException(error.message);
      }
    }
  }
}
