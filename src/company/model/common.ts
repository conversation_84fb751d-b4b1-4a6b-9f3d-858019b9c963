import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayNotEmpty, IsArray, IsBoolean, IsIn, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { values } from 'lodash';
// import { DateRangeRelative, NumberRange } from '@kezhaozhao/search-utils';
import { ConditionParams } from '../constant/common';
// import { KzzFilterMap } from '../constant/constant.kzz';
import { KysFilterMap } from '../constant/constant.kys';
import { DateRangeRelative, NumberRange } from '@kezhaozhao/qcc-model';

export class ESBool {
  must: object[];
  filter: object[];
  should: object[];
  must_not: object[];
}

export class KeywordAnalyze {
  @ApiPropertyOptional({ description: '原始关键词', type: String })
  originalKeyword: string;
  @ApiPropertyOptional({ description: '是否单个词', type: Boolean })
  singleWord: boolean;
  @ApiPropertyOptional({ description: '识别出的词', type: String })
  recognizedKeywords: string;
  @ApiPropertyOptional({ description: '未识别出的词', type: String })
  unrecognizedKeywords: string;
  @ApiPropertyOptional({ description: '识别词的类型', type: String })
  mode: string;
  @ApiPropertyOptional({ description: '关键词结果归类', type: Object })
  groupByKeywordType: object;
}

export class OperInfo {
  @ApiPropertyOptional({ description: 'KeyNo', type: String })
  k: string;
  @ApiPropertyOptional({ description: 'org OperInfoOrg', type: String })
  o: number;
  @ApiPropertyOptional({
    description: 'orgtype: 1-法定代表人, 2-执行事务合伙人 , 3-负责人 , 4-经营者 , 5-投资人 , 6-董事长 , 7-理事长 , 8-代表人',
    type: Number,
  })
  t: number;
  h: boolean;
}

export const RuleType = {
  Group: 'group',
  Rule: 'rule',
  RuleGroup: 'ruleGroup',
};

export const RuleOperator = {
  Contain: 'contain',
  ContainNot: 'containNot',
  All: 'all',
};

export const GroupLogic = {
  All: 'all', // and
  Any: 'any', // OR
};

// 搜索场景
export const SearchSence = {
  AdvancedSearch: 'AdvancedSearch',
  AgencySearch: 'AgencySearch',
};

export class Location {
  /**
   * 经度   "31.31297793454742"
   */
  @ApiPropertyOptional({
    description: '经度',
    type: String,
    maxLength: 2,
    required: true,
  })
  @IsNotEmpty()
  lat: string;

  /**
   * 纬度  "120.78272609849135"
   */
  @ApiPropertyOptional({
    description: '纬度',
    type: String,
    maxLength: 2,
    required: true,
  })
  @IsNotEmpty()
  lon: string;
}

export class Circular {
  /**
   * 半径
   */
  @ApiPropertyOptional({
    description: '半径',
    type: Number,
    required: true,
  })
  @IsNotEmpty()
  distance: number;

  @ApiPropertyOptional({
    description: '半径单位 m、km',
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsIn(['m', 'km'])
  unit: string;

  /**
   * 圆点坐标
   */
  @ApiPropertyOptional({
    description: '圆点坐标',
    type: Location,
    required: true,
  })
  @Type(() => Location)
  @ValidateNested()
  @IsNotEmpty()
  point: Location;
}

export class MapArea {
  @ApiPropertyOptional({
    description: '圆形区域',
    type: Circular,
    required: false,
  })
  @Type(() => Circular)
  @ValidateNested()
  @IsOptional()
  distance?: Circular;

  @ApiPropertyOptional({
    description: '不规则图形',
    type: Location,
    required: false,
  })
  @Type(() => Location)
  @ValidateNested()
  @IsArray()
  @IsOptional()
  polygon?: Location[];
}
export class SearchRule {
  @ApiProperty({
    description: '字段名称',
    type: String,
    isArray: false,
    required: true,
    enum: Object.keys(KysFilterMap),
  })
  @IsNotEmpty()
  @IsIn(Object.keys(KysFilterMap))
  field: string;

  @ApiProperty({
    description: '操作符 contain:包含, containNot: 不包含, all: 包含全部',
    type: String,
    isArray: false,
    required: true,
    enum: values(RuleOperator),
  })
  @IsNotEmpty()
  @IsIn(values(RuleOperator))
  operator = 'contain';

  @ApiProperty({
    description: '匹配值',
    type: Object,
    required: true,
  })
  @IsNotEmpty()
  value: object;

  @ApiProperty({
    description: '特殊过滤条件（地图）',
    type: MapArea,
    required: false,
  })
  @Type(() => MapArea)
  @IsOptional()
  filter?: MapArea;

  @ApiProperty({
    description: '特殊过滤条件（时间区间时，是否允许开始或结束时间有一个为空）',
    type: MapArea,
    required: false,
  })
  @Type(() => Boolean)
  @IsOptional()
  nullable?: false;
}
export class RuleNode {
  @ApiPropertyOptional({
    description: '子节点类型 group / rule / ruleGroup',
    type: String,
    isArray: false,
    required: false,
    enum: values(RuleType),
  })
  @IsIn(values(RuleType))
  @IsNotEmpty()
  type: string;

  @ApiPropertyOptional({
    description: '具体查询条件节点',
    type: SearchRule,
    isArray: false,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @Type(() => SearchRule)
  @ValidateNested({ each: true })
  searchRule: SearchRule;

  @ApiPropertyOptional({
    description: '子文档组合查询条件，内部and的关系',
    type: SearchRule,
    isArray: true,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @Type(() => SearchRule)
  @ValidateNested({ each: true })
  ruleGroup: SearchRule[];

  @ApiPropertyOptional({
    description: '查询分组节点',
    isArray: false,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  @Type(() => SearchGroup)
  @ValidateNested({ each: true })
  searchGroup: any;
}

export class SearchGroup {
  // any  all
  @ApiPropertyOptional({
    description: '子节点的逻辑关系 all=> AND, any => OR',
    type: String,
    isArray: false,
    required: true,
    enum: values(GroupLogic),
  })
  @IsIn(values(GroupLogic))
  @IsNotEmpty()
  logic: string;

  @ApiPropertyOptional({
    description: '子节点数组',
    type: RuleNode,
    isArray: true,
    required: true,
  })
  @IsArray()
  @Type(() => RuleNode)
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  children: RuleNode[];
}

/**
 * 行政区域 { pr: 省份代码 province, ct: 4位地市代码 city, dt: 6位区县代码 district }
 */
export class Region {
  /**
   * 省份代码  province
   */
  @ApiPropertyOptional({ description: '省份代码', type: String, required: true })
  @IsOptional()
  pr: string;
  /**
   * 4位地市代码  city
   */
  @ApiPropertyOptional({
    description: '4位地市代码',
    type: String,
    maxLength: 4,
    required: false,
  })
  @IsOptional()
  ct?: string;

  /**
   * 6位区县代码  district
   */
  @ApiPropertyOptional({
    description: '6位区县代码',
    type: String,
    maxLength: 6,
    required: false,
  })
  @IsOptional()
  dt?: string;
}

/**
 * 国民行业 { i1: 国民行业1级 industry_l1, i2: 国民行业2级 industry_l2, i3: 国民行业3级 industry_l3, i4: 国民行业4级 industry_l4 }
 */
export class Industry {
  /**
   * 国民行业1级  industry_l1
   */
  @ApiPropertyOptional({
    description: '国民行业1级',
    type: String,
    maxLength: 2,
    required: true,
  })
  @IsOptional()
  i1: string;

  /**
   * 国民行业2级  industry_l2
   */
  @ApiPropertyOptional({
    description: '国民行业2级',
    type: String,
    maxLength: 4,
    required: false,
  })
  @IsOptional()
  i2?: string;

  /**
   * 国民行业3级  industry_l3
   */
  @ApiPropertyOptional({
    description: '国民行业3级',
    type: String,
    maxLength: 6,
    required: false,
  })
  @IsOptional()
  i3?: string;

  /**
   * 国民行业4级  industry_l4
   */
  @ApiPropertyOptional({
    description: '国民行业4级',
    type: String,
    maxLength: 8,
    required: false,
  })
  @IsOptional()
  i4?: string;
}

/**
 * 企业联系方式
 */
export class ContactInfo {
  // 有无座机 has_telephones
  @ApiPropertyOptional({ description: '有无座机', type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  ht?: boolean;
  // 有无手机号码 has_mobilephones
  @ApiPropertyOptional({ description: '有无手机号码', type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  hm?: boolean;
  // 有无邮箱  has_emails
  @ApiPropertyOptional({ description: '有无邮箱', type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  he?: boolean;
}

export class SearchFilter {
  @ApiPropertyOptional({
    description: 'flag的terms过滤',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  termsFlag?: string[];

  @ApiPropertyOptional({
    description: 'companyIDs',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsNotEmpty({ each: true })
  @IsString({ each: true })
  ids?: string[];
  /**
   * status(状态描述)/statuscode(状态code)
   * 企业经营状态
   * 10: '在业', 20: '存续', 30: '筹建', 40: '清算', 50: '迁入', 60: '迁出', 70: '停业', 80: '撤销', 90: '吊销', 99: '注销'
   */
  @ApiPropertyOptional({
    description: '企业经营状态 10: 在业, 20: 存续, 30: 筹建, 40: 清算, 50: 迁入, 60: 迁出, 70: 停业, 80: 撤销, 90: 吊销, 99: 注销',
    type: String,
    isArray: true,
    enum: Object.keys(ConditionParams.statusCode),
  })
  // @IsIn(Object.keys(ConditionParams.statusCode), { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  sc?: string[];

  /**
   * areacode(市/区县代码)/province(省份代码)/provincedesc(省份描述)
   * region 行政区域  { pr: 省份代码 province, ac: 市/区县代码  areacode }
   */
  @ApiPropertyOptional({
    description: 'region 行政区域对象数组 [{ pr: 省份代码 province, ac: 市/区县代码  areacode }]',
    type: Region,
    isArray: true,
  })
  @Type(() => Region)
  @IsOptional()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Region)
  r?: Region[];

  /**
   * industry
   * subInd
   */
  @ApiPropertyOptional({
    description: '行业分类',
    isArray: true,
    type: Industry,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Industry)
  i?: Industry[];

  /**
   * b2bproductcategory b2b商品行业分类
   */
  @ApiPropertyOptional({
    description: 'b2b商品行业分类',
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => String)
  b2bpc?: string[];

  /**
   * 注册资本区间  registcapi(注册资本描述)/registcapiamount(注册资本数值)
   */
  @ApiPropertyOptional({
    description: '注册资本区间 100万以下、100-200万、200-500万、500-1000万、1000万以上、自定义，区间值为半闭区间，如100-200为[100，200)',
    type: NumberRange,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => NumberRange)
  rca?: NumberRange[];

  /**
   * registerunit
   * 注册资本类型，USD, HKD,CNY，OTHER
   */
  @ApiPropertyOptional({
    description: '注册资本币种 regist_capital_unit, CNY: 人民币, TWD: 台币, USD: 美元, HKD: 港元, EUR: 欧元, JPY: 日元, OTHER: 其他 ',
    isArray: true,
    type: String,
    enum: Object.keys(ConditionParams.registCapitalUnit),
  })
  @IsIn(Object.keys(ConditionParams.registCapitalUnit), { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  ru?: string[];

  /**
   * startdateyear/startdatecode
   * 近1周、1年以内、1-5年、5-10年、10-15年、15年以上、自定义，区间值为半闭区间，如1-5年为[1，5)
   */
  @ApiPropertyOptional({
    description: '成立日期区间',
    isArray: false,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @IsOptional()
  @ValidateNested()
  sd?: DateRangeRelative;

  /**
   * latestchangedate
   * 近1周、1年以内、1-5年、5-10年、10-15年、15年以上、自定义，区间值为半闭区间，如1-5年为[1，5)
   */
  @ApiPropertyOptional({
    description: '最新变更日期(工商信息)',
    isArray: false,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @IsOptional()
  @ValidateNested()
  lcd?: DateRangeRelative;

  /**
   * udpatedate
   * 近1周、1年以内、1-5年、5-10年、10-15年、15年以上、自定义，区间值为半闭区间，如1-5年为[1，5)
   */
  @ApiPropertyOptional({
    description: '变更日期(工商信息)',
    isArray: false,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @IsOptional()
  @ValidateNested()
  ud?: DateRangeRelative;

  /**
   * 最新纳税信用等级   latesttaxlevel
   * A: A级；  N_A:非A级；
   */
  @ApiPropertyOptional({
    description: '最新纳税信用等级  latesttaxlevel  A: A级；  N_A:非A级；',
    type: String,
    isArray: false,
    enum: ['A', 'N_A'],
  })
  @IsIn(['A', 'N_A'])
  @IsOptional()
  ltl?: string;

  /**
   * 是否是高新高新企业   tag
   * 是:Y  否:N
   */
  @ApiPropertyOptional({
    description: '是否是高新高新企业  tag  是:Y；  否:N；',
    type: String,
    isArray: false,
    enum: ['Y', 'N'],
  })
  @IsIn(['Y', 'N'])
  @IsOptional()
  gxqy?: string;

  /**
   * econkind(企业类型描述)/econkindcode(企业类型code)
   * 70 （个体工商户）  *********
   * 10 （有限责任公司）， 78546473
   * 50 （独资企业），40635967
   * 0   全民所有制  6986325
   * 90   集体所有制  3668533
   * 60   合伙制企业  1305650
   * 100  有限合伙  955163
   * 40  外商投资企业分公司  819893
   * 20  其他股份有限公司分公司（上市） 711195
   * 30  国企  683401
   * 130  台、港、澳投资企业分支机构  594515
   * 120  股份合作制分支机构  377596
   * 110  普通合伙  328891
   * 140  内资非法人企业、非公司私营企业  108613
   * 80  联营企业  79071
   */
  @ApiPropertyOptional({
    description:
      '公司类型,EconKindCode 10（有限责任公司）， 20（股份有限公司）， 30（国企）， 40（外企），50（独资企业）， 60（合伙制企业）， 70（个体工商户）, 80-联营企业, 90-集体所有制, 100-有限合伙, 110-普通合伙',
    type: String,
    isArray: true,
    enum: Object.keys(ConditionParams.econKindCode),
  })
  // @IsIn(Object.keys(ConditionParams.econKindCode), { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  ekc?: string[];

  /**
   * type
   * 组织机构类型
   * 0: '大陆公司', 1: '社会组织', 3: '香港公司', 4: '除基金会、事业单位、律师事务所之外的',
   * 5: '台湾公司', 10: '基金会', 11: '事业单位', 12: '律师事务所'
   */
  @ApiPropertyOptional({
    description:
      '组织机构类型 type, 0: 大陆公司, 1: 社会组织, 3: 香港公司, 4: 除基金会、事业单位、律师事务所之外的, 5: 台湾公司, 10: 基金会, 11: 事业单位, 12: 律师事务所',
    isArray: true,
    type: String,
    enum: Object.keys(ConditionParams.orgType),
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsOptional()
  @IsIn(Object.keys(ConditionParams.orgType), { each: true })
  ot?: string[];

  /**
   * insuredcount 参保人数
   * 5人以下、5-10人、10-20人、20-100人、100-500人、500-1000人、1000人以上、自定义，区间值为半闭区间，如10-20人为[10，20)
   */
  @ApiPropertyOptional({
    description: '参保人数区间 InsuredCount',
    type: NumberRange,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => NumberRange)
  ic?: NumberRange[];

  /**
   * 企业联系方式（内部或的关系）
   */
  @ApiPropertyOptional({
    description: '企业联系方式，{ ht: (有无座机), hm:(有无手机号码), he:(有无邮箱) }',
    isArray: false,
    type: ContactInfo,
  })
  @Type(() => ContactInfo)
  @IsOptional()
  @ValidateNested()
  ci?: ContactInfo;

  /**
   * 融资阶段code    financinglevel
   * 天使/种子轮、Pre-A至A+轮、Pre-B至B+轮、C轮及以上、收购/并购/被并购、战略投资、其他
   */
  @ApiPropertyOptional({
    description: '融资阶段code financinglevel',
    type: String,
    isArray: true,
    enum: Object.keys(ConditionParams.financeStageType),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsIn(Object.keys(ConditionParams.financeStageType), { each: true })
  fl?: string[];

  /**
   * listingstatuskw 上市状态，
   */
  @ApiPropertyOptional({
    description: '上市板块,A股：2,中概股：7,港股：6,科创板：501,新三板：1',
    type: String,
    isArray: true,
    enum: Object.keys(ConditionParams.listingStatusInfo),
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => String)
  @IsIn(Object.keys(ConditionParams.listingStatusInfo), { each: true })
  lst?: string[];

  /**
   * 企业信息tag
   */
  @ApiPropertyOptional({
    description: '上市信息 tag',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  tag?: string[];

  /**
   * vip 查询 flag
   */
  @ApiPropertyOptional({
    description: 'vip flag',
    type: String,
    isArray: true,
    enum: Object.keys(ConditionParams.flag).concat(Object.keys(ConditionParams.flag).map((k) => 'N_' + k)),
  })
  @IsIn(Object.keys(ConditionParams.flag).concat(Object.keys(ConditionParams.flag).map((k) => 'N_' + k)), { each: true })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  flag?: string[];

  /**
   * 圆形范围查询
   */
  @ApiPropertyOptional({
    description: '圆形范围查询 { distance: (半径), unit:(m/km), point: {lat:(经度), lon:(纬度)} }',
    isArray: false,
    type: Circular,
  })
  @Type(() => Circular)
  @IsOptional()
  @ValidateNested()
  distance?: Circular;

  /**
   * 不规则图形范围查询
   */
  @ApiPropertyOptional({
    description: '不规则图形范围查询 [{lat:(经度), lon:(纬度)}]',
    isArray: true,
    type: Location,
  })
  @Type(() => Location)
  @IsOptional()
  @ArrayNotEmpty()
  polygon?: Location[];

  /**
   * 企查查认证等级   qccauthenticationlevel  -1未认证,0普通认证、1高级认证、2超级认证、3联合认证',
   * QCCL: 有认证;  N_QCCL:无认证;
   */
  @ApiPropertyOptional({
    description: '最新纳税信用等级  qccauthenticationlevel  QCCL: 有认证;  N_QCCL:无认证;',
    type: String,
    isArray: false,
    enum: ['QCCL', 'N_QCCL'],
  })
  @IsIn(['QCCL', 'N_QCCL'])
  @IsOptional()
  qccl?: string;

  @ApiPropertyOptional({
    description: '营收水平万元',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  zjrhc?: string[];

  @ApiPropertyOptional({
    description: '利润水平万元',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  zjphc?: string[];

  @ApiPropertyOptional({
    description: '资产规模',
    type: String,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  zjasc?: string[];

  /**
   * personlist 主要人员姓名
   */
  @ApiPropertyOptional({
    description: '主要人员姓名',
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => String)
  psl?: string[];
}

/**
 * 企业联系方式
 */
export class CommonListItem {
  @ApiPropertyOptional({
    description:
      '风险扫苗自身风险 SelfCount = 1, 司法案件 JudicialCase = 2, 合作风险 CoopTotal = 3, 集团信息 GroupInfo = 4, 手机号码状态 PhoneState = 5, 网址状态 WebsiteState = 6, 英文名来源 EngSource = 7, 风险扫苗关联风险 RelatedCount = 8, 董事长、总经理、大股东信息 EmployeInfo = 9, MultipleOper MultipleOper = 10, IpoOpers IpoOpers = 11, 债券信息 Bond = 12, 风险扫苗自身风险 SelfCountV2 = 13, 风险扫苗关联风险 RelatedCountV2 = 14, 风险扫描关联风险（不带提示） RelatedCountV3 = 15',
    type: Boolean,
    required: false,
  })
  k: string;
  @ApiPropertyOptional({ description: '', type: Boolean, required: false })
  v: string;
}
