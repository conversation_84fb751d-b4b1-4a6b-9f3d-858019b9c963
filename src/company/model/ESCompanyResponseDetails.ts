// import { ApiPropertyOptional } from '@nestjs/swagger';
// import { Case, ChinaCourt, Copr, Cret, Customs, Email, Exception, License, Patent, Scopr, Tel, Telicense, Tender, Trademark } from './response.model';

// /**
//  * ES返回字段详情
//  */
// export class ESCompanyResponseDetails {
//   //企业地址       address
//   @ApiPropertyOptional({ description: '注册地址', type: String })
//   address?: string;

//   //年报地址       address2
//   @ApiPropertyOptional({ description: '年报地址', type: String })
//   address2?: string;

//   //行政许可数量       adlicensecount
//   @ApiPropertyOptional({ description: '行政许可数量', type: Number })
//   adlicensecount?: number;

//   //所属地区(省市区3级)      areacode
//   @ApiPropertyOptional({ description: '所属地区(省市区3级) ', type: String })
//   areacode?: string;

//   //裁判文书    case
//   @ApiPropertyOptional({ description: '裁判文书', type: Array })
//   case?: Case[];

//   //变更日期       changedate
//   @ApiPropertyOptional({ description: '变更日期', type: Array })
//   changedate?: Date[];

//   //变更类型(变更注册资金、变更公司名称、变更股东或法人、变更地址所、变更经营范围)       changetype
//   @ApiPropertyOptional({
//     description: '变更类型(变更注册资金、变更公司名称、变更股东或法人、变更地址所、变更经营范围)',
//     type: Array,
//   })
//   changetype?: string[];

//   //法院公告       chinacourt
//   @ApiPropertyOptional({ description: '法院公告', type: Array })
//   chinacourt?: ChinaCourt[];

//   //作品著作权    copr
//   @ApiPropertyOptional({ description: '作品著作权', type: Array })
//   copr?: Copr[];

//   //作品著作权数量       coprcount
//   @ApiPropertyOptional({ description: '作品著作权数量', type: Number })
//   coprcount?: number;

//   //证书信息       cret
//   @ApiPropertyOptional({ description: '证书信息', type: Array })
//   cret?: Cret[];

//   //证书数量      cretcount
//   @ApiPropertyOptional({ description: '证书数量', type: Number })
//   cretcount?: number;

//   //进出口信用信息       customs
//   @ApiPropertyOptional({ description: '进出口信用', type: Array })
//   customs?: Customs[];

//   //企业类型(有限责任公司/股份有限公司/国企/外商投资企业/独资企业/个体工商户/联合企业/集体所有制/有限合伙/普通合伙)       econkind
//   @ApiPropertyOptional({
//     description: '企业类型(有限责任公司/股份有限公司/国企/外商投资企业/独资企业/个体工商户/联合企业/集体所有制/有限合伙/普通合伙)',
//     type: String,
//   })
//   econkind?: string;

//   //企业类型code       econkindcode
//   @ApiPropertyOptional({ description: '企业类型code', type: String })
//   econkindcode?: string;

//   //邮箱来源渠道(1：年报，3：手动，4：阿里巴巴，5：慧聪网，6：Atobo,7：邓白氏)       emailsource
//   @ApiPropertyOptional({
//     description: '邮箱来源渠道(1：年报，3：手动，4：阿里巴巴，5：慧聪网，6：Atobo,7：邓白氏)',
//     type: String,
//   })
//   emailsource?: string;

//   //经营异常      exception
//   @ApiPropertyOptional({ description: '经营异常', type: Array })
//   exception?: Exception[];

//   //融资阶段(0-无融资信息、 1-天使/种子轮、2-Pre-A至A+轮、3-Pre-B至B+轮、4-C轮及以上、5-新三板/上市、6-收购/并购/被并购、7-战略投资、8-其他)       financinglevel
//   @ApiPropertyOptional({
//     description: '融资阶段(0-无融资信息、 1-天使/种子轮、2-Pre-A至A+轮、3-Pre-B至B+轮、4-C轮及以上、5-新三板/上市、6-收购/并购/被并购、7-战略投资、8-其他)',
//     type: String,
//   })
//   financinglevel?: string[];

//   //融资次数       financinground
//   @ApiPropertyOptional({ description: '融资次数', type: Number })
//   financinground?: number;

//   //企业信息标志       flag
//   @ApiPropertyOptional({ description: '企业信息标志', type: Array })
//   flag?: string[];

//   //企业名称全称       fullname
//   @ApiPropertyOptional({ description: '企业名称全称', type: String })
//   fullname?: string;

//   //ICP备案数量       icpcount
//   @ApiPropertyOptional({ description: 'ICP备案数量', type: Number })
//   icpcount?: number;

//   //企业信息id       id
//   @ApiPropertyOptional({ description: '企业信息id', type: String })
//   id?: string;

//   //国民行业一级大分类       industry
//   @ApiPropertyOptional({ description: '国民行业一级大分类', type: String })
//   industry?: string;

//   //参保人数       insuredcount
//   @ApiPropertyOptional({ description: '参保人数', type: String })
//   insuredcount?: string;

//   //企业简介       introduction
//   @ApiPropertyOptional({ description: '企业简介', type: String })
//   introduction?: string;

//   //无效状态商标数量       invalidpatentcount
//   @ApiPropertyOptional({ description: '无效状态商标数量', type: Number })
//   invalidpatentcount?: number;

//   //信息是否有效(0无效,1有效,2删除)       isvalid
//   @ApiPropertyOptional({
//     description: '信息是否有效(0无效,1有效,2删除)',
//     type: String,
//   })
//   isvalid?: string;

//   //最新司法拍卖时间       lastauctionsdate
//   @ApiPropertyOptional({ description: '最新司法拍卖时间', type: Date })
//   lastauctionsdate?: Date;

//   //最新裁判文书发布日期       lastcasedate
//   @ApiPropertyOptional({ description: '最新裁判文书发布日期', type: Date })
//   lastcasedate?: Date;

//   //最新获证日期       lastcertificatedate
//   @ApiPropertyOptional({ description: '最新获证日期', type: Date })
//   lastcertificatedate?: Date;

//   //最新刊登日期(法院公告)       lastchinacourtdate
//   @ApiPropertyOptional({ description: '最新刊登日期(法院公告)', type: Date })
//   lastchinacourtdate?: Date;

//   //最新开庭公告开庭时间       lastcourtnoticedate
//   @ApiPropertyOptional({ description: '最新开庭公告开庭时间', type: Date })
//   lastcourtnoticedate?: Date;

//   //最新信用评级时间       lastcreditratingdate
//   @ApiPropertyOptional({ description: '最新信用评级时间', type: Date })
//   lastcreditratingdate?: Date;

//   //最新终本案件立案日期       lastendexecutioncasedate
//   @ApiPropertyOptional({ description: '最新终本案件立案日期', type: Date })
//   lastendexecutioncasedate?: Date;

//   //最新环保处罚日期       lastenvpubdate
//   @ApiPropertyOptional({ description: '最新环保处罚日期', type: Date })
//   lastenvpubdate?: Date;

//   //最新询价评估发布日期       lastevaluationdate
//   @ApiPropertyOptional({ description: '最新询价评估发布日期', type: Date })
//   lastevaluationdate?: Date;

//   //最新列入经营异常日期       lastexceptionsdate
//   @ApiPropertyOptional({ description: '最新列入经营异常日期', type: Date })
//   lastexceptionsdate?: Date;

//   //最新投资时间       lastinvestmentdate
//   @ApiPropertyOptional({ description: '最新投资时间', type: Date })
//   lastinvestmentdate?: Date;

//   //最新行政许可日期(资质证书)       lastlicensedate
//   @ApiPropertyOptional({
//     description: '最新行政许可日期(资质证书)',
//     type: Date,
//   })
//   lastlicensedate?: Date;

//   //最新税收违法发布日期       lastmajortaxillegaldate
//   @ApiPropertyOptional({ description: '最新税收违法发布日期', type: Date })
//   lastmajortaxillegaldate?: Date;

//   //最新土地转让成交日期       lastmarketdate
//   @ApiPropertyOptional({ description: '最新土地转让成交日期', type: Date })
//   lastmarketdate?: Date;

//   //最新土地抵押截止日期       lastmortgagedate
//   @ApiPropertyOptional({ description: '最新土地抵押截止日期', type: Date })
//   lastmortgagedate?: Date;

//   //最新动产抵押日期       lastmpledgedate
//   @ApiPropertyOptional({ description: '最新动产抵押日期', type: Date })
//   lastmpledgedate?: Date;

//   //最新不动产抵押日期       lastpledgedate
//   @ApiPropertyOptional({ description: '最新不动产抵押日期', type: Date })
//   lastpledgedate?: Date;

//   //       lastproeprtydate
//   @ApiPropertyOptional({ description: '', type: Date })
//   lastproeprtydate?: Date;

//   //最新地块公示日期       lastpublicitydate
//   @ApiPropertyOptional({ description: '最新地块公示日期', type: Date })
//   lastpublicitydate?: Date;

//   //最新公示催告公告日期       lastpublicnoticedate
//   @ApiPropertyOptional({ description: '最新公示催告公告日期', type: Date })
//   lastpublicnoticedate?: Date;

//   //最新行政处罚决定日期       lastpunishmentdate
//   @ApiPropertyOptional({ description: '最新行政处罚决定日期', type: Date })
//   lastpunishmentdate?: Date;

//   //最新购地合同签订日期       lastpurchasedate
//   @ApiPropertyOptional({ description: '最新购地合同签订日期', type: Date })
//   lastpurchasedate?: Date;

//   //最新诉讼日期       lastregisterdate
//   @ApiPropertyOptional({ description: '最新诉讼日期', type: Date })
//   lastregisterdate?: Date;

//   //最新移出经营异常日期       lastremoveexceptionsdate
//   @ApiPropertyOptional({ description: '最新移出经营异常日期', type: Date })
//   lastremoveexceptionsdate?: Date;

//   //最新限制高消费发布日期       lastsumptuarydate
//   @ApiPropertyOptional({ description: '最新限制高消费发布日期', type: Date })
//   lastsumptuarydate?: Date;

//   //最新失信发生日期       lastsxdate
//   @ApiPropertyOptional({ description: '最新失信发生日期', type: Date })
//   lastsxdate?: Date;

//   //最新欠税公告金额       lasttaxoweamount
//   @ApiPropertyOptional({ description: '最新欠税公告金额', type: Number })
//   lasttaxoweamount?: number;

//   //最新欠税公告发布日期       lasttaxowedate
//   @ApiPropertyOptional({ description: '最新欠税公告发布日期', type: Date })
//   lasttaxowedate?: Date;

//   //最新被执行人立案日期       lastzxdate
//   @ApiPropertyOptional({ description: '最新被执行人立案日期', type: Date })
//   lastzxdate?: Date;

//   //最新变更日期(工商信息)       latestchangedate
//   @ApiPropertyOptional({ description: '最新变更日期(工商信息)', type: Date })
//   latestchangedate?: Date;

//   //最新工商信息变更类型(变更注册资金、变更公司名称、变更股东或法人、变更地址所、变更经营范围)       latestchangetype
//   @ApiPropertyOptional({
//     description: '最新工商信息变更类型(变更注册资金、变更公司名称、变更股东或法人、变更地址所、变更经营范围)',
//     type: String,
//   })
//   latestchangetype?: string;

//   //最新融资金额       latestfinancingamount
//   @ApiPropertyOptional({ description: '最新融资金额', type: Number })
//   latestfinancingamount?: number;

//   //最新融资日期       latestfinancingdate
//   @ApiPropertyOptional({ description: '最新融资日期', type: Date })
//   latestfinancingdate?: Date;

//   //最新招聘日期       latestrecruitmentdate
//   @ApiPropertyOptional({ description: '最新招聘日期', type: Date })
//   latestrecruitmentdate?: Date;

//   //A级纳税人评价级别(最新的一个评价年度)       latesttaxlevel
//   @ApiPropertyOptional({
//     description: 'A级纳税人评价级别(最新的一个评价年度)',
//     type: String,
//   })
//   latesttaxlevel?: string;

//   //A级纳税人评价年度(最新的一个评价年度)       latesttaxyear
//   @ApiPropertyOptional({
//     description: 'A级纳税人评价年度(最新的一个评价年度)',
//     type: Number,
//   })
//   latesttaxyear?: number;

//   //行政许可内容(资质证书)       license
//   @ApiPropertyOptional({ description: '行政许可内容(资质证书)', type: Array })
//   license?: License[];

//   //上市日期       listdate
//   @ApiPropertyOptional({ description: '上市日期', type: Date })
//   listdate?: Date;

//   //最近一年公开专利数量       lypubpatentcount
//   @ApiPropertyOptional({ description: '最近一年公开专利数量', type: Number })
//   lypubpatentcount?: number;

//   //经营异常数量       manageexceptioncount
//   @ApiPropertyOptional({ description: '经营异常数量', type: Number })
//   manageexceptioncount?: number;

//   //企业名称(含曾用名)       name
//   @ApiPropertyOptional({ description: '企业名称(含曾用名)', type: String })
//   name?: string;

//   //企业名称(查询专用)        namesearch
//   @ApiPropertyOptional({ description: '企业名称(查询专用)', type: String })
//   namesearch?: string;

//   //初次获证日期(证书信息)       oldcertificatedate
//   @ApiPropertyOptional({ description: '初次获证日期(证书信息)', type: Date })
//   oldcertificatedate?: Date;

//   //组织机构代码       orgno
//   @ApiPropertyOptional({ description: '组织机构代码', type: String })
//   orgno?: string;

//   //统一社会信用代码       creditcode
//   @ApiPropertyOptional({ description: '统一社会信用代码', type: String })
//   creditcode?: string;

//   //纳税人识别号       taxno
//   @ApiPropertyOptional({ description: '纳税人识别号', type: String })
//   taxno?: string;

//   //注册号       regno
//   @ApiPropertyOptional({ description: '注册号', type: String })
//   regno?: string;

//   //企业曾用名       originalname
//   @ApiPropertyOptional({ description: '企业曾用名', type: Array })
//   originalname?: string[];

//   //专利信息(发明公布、发明授权、实用新型、外观设计)       patent
//   @ApiPropertyOptional({
//     description: '专利信息(发明公布、发明授权、实用新型、外观设计)',
//     type: Array,
//   })
//   patent?: Patent[];

//   //专利数量       patentcount
//   @ApiPropertyOptional({ description: '专利数量', type: Number })
//   patentcount?: number;

//   //招聘总人数(近3个月)       personnelcount
//   @ApiPropertyOptional({ description: '招聘总人数(近3个月)', type: Number })
//   personnelcount?: number;

//   //手机来源渠道(1：年报，3：手动，4：阿里巴巴，5：慧聪网，6：Atobo,7：邓白氏)       phonesource
//   @ApiPropertyOptional({
//     description: '手机来源渠道(1：年报，3：手动，4：阿里巴巴，5：慧聪网，6：Atobo,7：邓白氏)',
//     type: String,
//   })
//   phonesource?: string;

//   //       pid
//   @ApiPropertyOptional({ description: '', type: String })
//   pid?: string;

//   //最近1个月招聘岗位名称       position
//   @ApiPropertyOptional({ description: '最近1个月招聘岗位名称', type: String })
//   position?: string;

//   //招聘岗位总数(近3个月)       positionscount
//   @ApiPropertyOptional({ description: '招聘岗位总数(近3个月)', type: Number })
//   positionscount?: number;

//   //主营产品       product
//   @ApiPropertyOptional({ description: '主营产品', type: String })
//   product?: string;

//   //省份code       province
//   @ApiPropertyOptional({ description: '省份code', type: String })
//   province?: string;

//   //省份描述       provincedesc
//   @ApiPropertyOptional({ description: '省份描述', type: String })
//   provincedesc?: string | string[];

//   //行政处罚数量       punishcount
//   @ApiPropertyOptional({ description: '行政处罚数量', type: Number })
//   punishcount?: number;

//   //企查查code       qcccode
//   @ApiPropertyOptional({ description: '企查查code', type: String })
//   qcccode?: string;

//   //注册资本描述       registcapi
//   @ApiPropertyOptional({ description: '注册资本描述', type: String })
//   registcapi?: string;

//   //注册资本金额       registcapiamount
//   @ApiPropertyOptional({ description: '注册资本金额(万元)', type: Number })
//   registcapiamount?: number;

//   //注册资本类型(人民币/美元/其他)       registerunit
//   @ApiPropertyOptional({
//     description: '注册资本类型(人民币/美元/其他)',
//     type: String,
//   })
//   registerunit?: string;

//   //经营范围       scope
//   @ApiPropertyOptional({ description: '经营范围', type: String })
//   scope?: string;

//   //软件著作权       scopr
//   @ApiPropertyOptional({ description: '软件著作权', type: Array })
//   scopr?: Scopr[];

//   //软件著作权数量       scoprcount
//   @ApiPropertyOptional({ description: '软件著作权数量', type: Number })
//   scoprcount?: number;

//   //成立日期code       startdatecode
//   @ApiPropertyOptional({ description: '成立日期code', type: Number })
//   startdatecode?: number;

//   //成立年份       startdateyear
//   @ApiPropertyOptional({ description: '成立年份', type: Number })
//   startdateyear?: number;

//   //经营状态  status
//   @ApiPropertyOptional({ description: '经营状态', type: String })
//   status?: string;

//   //经营状态code       statuscode
//   @ApiPropertyOptional({ description: '经营状态code', type: String })
//   statuscode?: string;

//   //所属区域子分类       subind
//   @ApiPropertyOptional({ description: '所属区域子分类', type: Array })
//   subind?: string[];

//   //上市板块(A股、中概股、港股、科创板、新三板)       tag
//   @ApiPropertyOptional({
//     description: '上市板块(A股、中概股、港股、科创板、新三板)',
//     type: String,
//   })
//   tag?: string;

//   // //上市信息       tagsinfo
//   // @ApiPropertyOptional({ description: '上市信息', type: String })
//   // tagsinfo?: string;

//   //纳税人资格有效期       taxpayerdate
//   @ApiPropertyOptional({ description: '纳税人资格有效期', type: Date })
//   taxpayerdate?: Date;

//   //最近一年获取证书数量       tcretcount
//   @ApiPropertyOptional({ description: '最近一年获取证书数量', type: Number })
//   tcretcount?: number;

//   //固话信息       telicense
//   @ApiPropertyOptional({ description: '固话信息', type: Array })
//   telicense?: Telicense[];

//   //固话来源渠道(1：年报，3：手动，4：阿里巴巴，5：慧聪网，6：Atobo,7：邓白氏)       telsource
//   @ApiPropertyOptional({ description: '', type: String })
//   telsource?: string;

//   //招投标信息       tender
//   @ApiPropertyOptional({ description: '招投标信息', type: Array })
//   tender?: Tender[];

//   //总融资金额       totalfinancingamount
//   @ApiPropertyOptional({ description: '总融资金额', type: Number })
//   totalfinancingamount?: number;

//   //商标信息       trademark
//   @ApiPropertyOptional({ description: '商标信息', type: Array })
//   trademark?: Trademark[];

//   //组织机构(大陆企业/社会组织/香港企业/基金会/事业单位/律师事务所/美股企业)       type
//   @ApiPropertyOptional({
//     description: '组织机构(大陆企业/社会组织/香港企业/基金会/事业单位/律师事务所/美股企业)',
//     type: String,
//   })
//   type?: string;

//   //有效状态商标数量       validpatentcount
//   @ApiPropertyOptional({ description: '有效状态商标数量', type: Number })
//   validpatentcount?: number;

//   //网址信息       website
//   @ApiPropertyOptional({ description: '网址信息', type: String })
//   website?: string;

//   //打分权重信息       weight
//   @ApiPropertyOptional({ description: '打分权重信息', type: Number })
//   weight?: number;

//   //电话       contactnumber
//   @ApiPropertyOptional({ description: '电话', type: Array })
//   contactnumber?: string[];

//   //电话列表       tellist
//   @ApiPropertyOptional({ description: '电话列表', type: Array })
//   tellist?: Tel[];

//   @ApiPropertyOptional({ description: '电话数量', type: Number })
//   telCount?: number;

//   // 邮箱    email
//   @ApiPropertyOptional({ description: '邮箱', type: Array })
//   email?: string[];

//   //邮箱列表       emaillist
//   @ApiPropertyOptional({ description: '邮箱列表', type: Array })
//   emaillist?: Email[];

//   @ApiPropertyOptional({ description: '邮箱数量', type: Number })
//   emailCount?: number;

//   //法人代表       opername
//   @ApiPropertyOptional({ description: '法人代表', type: String })
//   opername?: string;

//   //登记机关       belongorg
//   @ApiPropertyOptional({ description: '等级机关', type: String })
//   belongorg?: string;

//   //动产抵押数量       mpledgecount
//   @ApiPropertyOptional({ description: '动产抵押数量', type: Number })
//   mpledgecount?: number;

//   //清算信息数量       liquidationcount
//   @ApiPropertyOptional({ description: '清算信息数量', type: Number })
//   liquidationcount?: number;

//   //司法案件数量       jacount
//   @ApiPropertyOptional({ description: '司法案件数量', type: Number })
//   jacount?: number;

//   //上市信息       listingstatus
//   @ApiPropertyOptional({ description: '上市信息', type: Number })
//   listingstatus?: number;

//   //经纬度       location
//   @ApiPropertyOptional({ description: '经纬度', type: Array })
//   location?: string;

//   @ApiPropertyOptional({ description: '号码状态 1 可用， 2 不可用  3 未知', type: Number })
//   telstatus?: number;

//   @ApiPropertyOptional({ description: 'B2B产品名称', type: Array })
//   b2bproduct: string[];

//   @ApiPropertyOptional({ description: 'B2B产品分类名称', type: Array })
//   b2bproductcategory: string[];

//   @ApiPropertyOptional({
//     description: '企查查认证等级：-1未认证,0普通认证、1高级认证、2超级认证、3联合认证',
//     type: String,
//   })
//   qccauthenticationlevel?: string;

//   @ApiPropertyOptional({
//     description: '实缴资本',
//     type: String,
//   })
//   reccap?: string;

//   @ApiPropertyOptional({
//     description: '实缴资本金额数字(万元)',
//     type: Number,
//   })
//   reccapamount?: number;

//   @ApiPropertyOptional({
//     description: '上市企业年度营业额(万元)',
//     type: Number,
//   })
//   yysramount?: number;

//   @ApiPropertyOptional({
//     description: '上市企业营业额公布年度',
//     type: Number,
//   })
//   yysryear?: number;

//   //有无logo       hasimage
//   @ApiPropertyOptional({ description: '有无logo 1-有', type: String })
//   hasimage?: string;

//   @ApiPropertyOptional({ description: '是否在crm 1-有', type: Number })
//   inCrm = 0;

//   @ApiPropertyOptional({ description: '合作企业数量', type: Number })
//   agentcomcount = 0;

//   @ApiPropertyOptional({ description: '合作总次数', type: Number })
//   agentcoopcount = 0;

//   @ApiPropertyOptional({ description: '企业潜力' })
//   flagqyql?;

//   @ApiPropertyOptional({ description: '新兴行业' })
//   tagxxhy?;

//   @ApiPropertyOptional({ description: '企业年龄' })
//   companyage?;

//   @ApiPropertyOptional({ description: '信用评分' })
//   creditlevel?;

//   // agentcomcount---合作企业数量
//   // agentcoopcount---合作总次数
// }
