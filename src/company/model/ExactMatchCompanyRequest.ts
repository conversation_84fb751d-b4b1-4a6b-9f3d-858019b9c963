import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsIn, IsNumber, IsOptional, MaxLength } from 'class-validator';

export class ExactMatchCompanyRequest {
  @ApiProperty({ description: '关键字', maxLength: 3000 })
  @MaxLength(500, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  searchKey: string[];

  @ApiPropertyOptional({ description: '需要返回的字段' })
  @MaxLength(30, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(20)
  @IsOptional()
  includeFields: string[];

  @ApiPropertyOptional({ description: '指定查询维度，默认值：name, taxno, creditcode, regno ' })
  @MaxLength(30, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(20)
  @IsOptional()
  @IsIn(['name', 'taxno', 'creditcode', 'regno', 'originalname'], { each: true })
  searchFields: string[];

  @ApiPropertyOptional({ description: '组织机构类型，默认值： 0: 大陆企业, 1: 社会组织, 11: 事业单位, 12: 律师事务所 ' })
  @MaxLength(30, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(20)
  @IsOptional()
  @IsIn(['0', '1', '11', '12'], { each: true })
  searchType: string[];

  @ApiPropertyOptional({ description: '是否允许重复(同一个名字找出多家公司)' })
  @IsNumber()
  @IsOptional()
  @IsIn([0, 1])
  allowDuplicated?: number;
}
