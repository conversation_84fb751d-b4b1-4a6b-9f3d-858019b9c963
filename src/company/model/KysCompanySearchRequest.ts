import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>th,
  Validate,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { KysSearchIndex } from '../constant/constant.kys';
import { SearchFilter, SearchGroup, SearchSence } from '.';
import { CheckSearchKey, ESBaseSearchRequest, REGEX } from '@kezhaozhao/qcc-model';

export class KysCompanySearchRequest extends ESBaseSearchRequest {
  @ApiProperty({
    required: false,
    type: Number,
    default: 10,
    example: 10,
    maximum: 100,
    description: '每页显示的记录数',
  })
  @IsNumber()
  @Min(1)
  @Max(1000)
  @Type(() => Number)
  @IsOptional()
  pageSize = 10;

  @ApiProperty({ description: '关键字', type: String, required: true })
  @IsNotEmpty()
  @MaxLength(2500, { message: 'Search Key max length is 2500' })
  @MinLength(2)
  @Validate(CheckSearchKey, [REGEX.invalidCharacters, REGEX.uuid], {
    message: 'searchKey is Invalid Characers!',
  })
  @Type(() => String)
  @IsOptional()
  searchKey: string;

  @ApiProperty({
    description: '查询的索引字段,值可能为：name, originalname, namesearch,product,scope,trademark,patent,tender,website,position，address',
    type: String,
    isArray: true,
    required: false,
    enum: KysSearchIndex,
  })
  @MaxLength(20, {
    each: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsIn(KysSearchIndex, { each: true })
  searchIndex?: string[];

  @ApiProperty({
    description: '过滤条件',
    isArray: false,
    type: () => SearchFilter,
    required: false,
  })
  @Type(() => SearchFilter)
  @IsOptional()
  @ValidateNested()
  filter?: SearchFilter;

  @ApiProperty({
    description: '高级搜索指定规则',
    type: () => SearchGroup,
    isArray: false,
    required: true,
  })
  @Type(() => SearchGroup)
  @IsNotEmpty()
  @IsOptional()
  @ValidateNested({ each: true })
  searchObject?: SearchGroup;

  @ApiProperty({
    description: '定制搜索指定规则',
    type: () => SearchGroup,
    isArray: false,
    required: true,
  })
  @Type(() => SearchGroup)
  @IsNotEmpty()
  @IsOptional()
  @ValidateNested({ each: true })
  customSearchObject?: SearchGroup;

  @ApiProperty({
    description: '是否高亮，默认 false 不高亮',
    type: Boolean,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @IsIn([true, false])
  isHighlight?: boolean = false;

  @ApiProperty({
    description: '是否ƒ返回无效公司，默认 false 不包括',
    type: Boolean,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @IsIn([true, false])
  includeInvalid?: boolean = false;

  @ApiProperty({
    description: '在过滤条件外，单独需要过滤的企业ID数组',
    type: String,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  excludeCompanyIds?: string[];

  @ApiProperty({
    description: '在过滤条件外，单独需要包含的企业ID数组',
    type: String,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  includeCompanyIds?: string[];

  @ApiProperty({
    description: '是否过滤 不可用号码  true-过滤， false(默认)-不过滤',
    type: Number,
    isArray: false,
    required: false,
  })
  @Type(() => Boolean)
  @IsOptional()
  @IsBoolean()
  excludeUnavailableTel?: boolean = false;

  @ApiProperty({
    description: '是否过滤 疑似代记账号码  true-过滤， false(默认)-不过滤',
    type: Number,
    isArray: false,
    required: false,
  })
  @Type(() => Boolean)
  @IsOptional()
  @IsBoolean()
  excludeBatTel?: boolean = false;

  @ApiProperty({
    description: '筛选的公司名单批次id',
    type: Number,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  companyList?: number[];

  @ApiProperty({
    description: '查询范围 company: 工商信息； tender：招投标信息',
    type: String,
    isArray: true,
    required: false,
  })
  @MaxLength(20, {
    each: true,
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  scope?: string[] = ['company'];

  @ApiProperty({
    description: '地图地址类型 0 全部 1 企业通讯地址（最新年报地址） 2注册地址',
    type: Number,
    isArray: false,
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  @IsIn([0, 1, 2])
  mapType?: number = 2;

  @ApiProperty({
    description: '应用场景 AdvancedSearch-拓客高级搜索； AgencySearch-同业竞争搜索',
    type: String,
    isArray: false,
    required: false,
  })
  @IsOptional()
  @IsIn(['AdvancedSearch', 'AgencySearch'])
  scene?: string = SearchSence.AdvancedSearch;

  @ApiProperty({
    description: '是否过滤组织内所有客户 true-过滤， false(默认)-不过滤',
    type: Boolean,
    isArray: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  excludeCustomer?: boolean = false;

  @ApiProperty({
    description: '是否过滤组织内所有线索 true-过滤， false(默认)-不过滤',
    type: Boolean,
    isArray: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  excludeLeads?: boolean = false;

  @ApiProperty({
    description: '是否过滤组织内所有解锁企业 true-过滤， false(默认)-不过滤',
    type: Boolean,
    isArray: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  excludeUnlock?: boolean = false;

  @ApiProperty({
    description: '',
    type: Number,
    isArray: false,
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  orgId?: number;

  @ApiPropertyOptional({ description: '聚合字段', required: false })
  @IsOptional()
  // @IsIn(['econkindcode', 'statuscode'], { each: true })
  aggFields?: string[];
}
