// import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
// import { ArrayMaxSize, ArrayMinSize, ArrayNotEmpty, IsOptional, MaxLength } from 'class-validator';

// export class PhoneMatchCompanyRequest {
//   @ApiProperty({ description: '手机号', maxLength: 3000 })
//   @MaxLength(200, { each: true })
//   @ArrayMinSize(1)
//   @ArrayMaxSize(100)
//   @ArrayNotEmpty()
//   phoneList: string[];

//   @ApiPropertyOptional({ description: '需要返回的字段' })
//   @MaxLength(30, { each: true })
//   @ArrayMinSize(1)
//   @ArrayMaxSize(20)
//   @IsOptional()
//   includeFields: string[];
// }
