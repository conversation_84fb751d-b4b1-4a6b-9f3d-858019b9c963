import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 裁判文书信息
 */
export class Case {
  //裁判文书标题    name
  @ApiPropertyOptional({ description: '裁判文书标题', type: String })
  name?: string;

  //裁判文书案由    casereason
  @ApiPropertyOptional({ description: '裁判文书案由', type: String })
  casereason?: string;
}

/**
 * 法院公告信息
 */
export class ChinaCourt {
  //法院公告类型(送达诉状副本及开庭传票、送达裁判文书、“申请宣告失踪、死亡”、送达执行文书、申请公示催告、送达破产文书、送达海事文书、送达仲裁文书、拍卖公告、清算公告、遗失声明、其它、更正、银行债权催收公告、版权公告)    categorycode
  @ApiPropertyOptional({
    description:
      '法院公告类型(送达诉状副本及开庭传票、送达裁判文书、“申请宣告失踪、死亡”、送达执行文书、申请公示催告、送达破产文书、送达海事文书、送达仲裁文书、拍卖公告、清算公告、遗失声明、其它、更正、银行债权催收公告、版权公告)',
    type: String,
  })
  categorycode?: string;
}

/**
 * 作评著作权信息
 */
export class Copr {
  //作品名称    name
  @ApiPropertyOptional({ description: '作品名称', type: String })
  name?: string;
}

/**
 * 证书信息
 */
export class Cret {
  //证书名称    name
  @ApiPropertyOptional({ description: '证书名称', type: String })
  name?: string;

  //涵盖证书类别    type
  @ApiPropertyOptional({ description: '涵盖证书类别', type: String })
  type?: string;

  //证书发证日期    startdate
  @ApiPropertyOptional({ description: '证书发证日期', type: Date })
  startdate?: Date;

  //证书有效截止日期    enddate
  @ApiPropertyOptional({ description: '证书有效截止日期', type: Date })
  enddate?: Date;
}

/**
 * 进出口信用信息
 */
export class Customs {
  //进出口信用状态(有效、无效)    cancellationflag
  @ApiPropertyOptional({
    description: '进出口信用状态(有效、无效)',
    type: String,
  })
  cancellationflag?: string;

  //海关信用类型    tradetype
  @ApiPropertyOptional({ description: '海关信用类型', type: String })
  tradetype?: string;

  //海关登记行业(国民经济行业分类)    industrytype
  @ApiPropertyOptional({
    description: '海关登记行业(国民经济行业分类)',
    type: String,
  })
  industrytype?: string;

  //海关登记经营类别(进出口货物收发货人、报关企业、报关企业分支机构、特殊监管区“双重身份”企业、临时注册登记、无进出口经营权加工生产企业、保税仓库、出口监管仓库、进出境运输工具负责人)   reggov
  @ApiPropertyOptional({
    description:
      '海关登记经营类别(进出口货物收发货人、报关企业、报关企业分支机构、特殊监管区“双重身份”企业、临时注册登记、无进出口经营权加工生产企业、保税仓库、出口监管仓库、进出境运输工具负责人)',
    type: String,
  })
  reggov?: string;

  //进出口信用注册日期   regdate
  @ApiPropertyOptional({ description: '进出口信用注册日期', type: Date })
  regdate?: Date;
}

/**
 * 经营异常信息
 */
export class Exception {
  //列入经营异常名录原因    addreason
  @ApiPropertyOptional({ description: '列入经营异常名录原因', type: String })
  addreason?: string;
}

/**
 * 资质证书信息
 */
export class License {
  //行政许可文件名称    name
  @ApiPropertyOptional({ description: '行政许可文件名称', type: String })
  name?: string;

  //行政许可内容    content
  @ApiPropertyOptional({ description: '行政许可内容', type: String })
  content?: string;

  //电信许可业务范围    scope
  @ApiPropertyOptional({ description: '电信许可业务范围', type: String })
  scope?: string;
}

/**
 * 专利信息
 */
export class Patent {
  //专利名称    title
  @ApiPropertyOptional({ description: '专利名称', type: String })
  title?: string;

  //专利类型(发明公布、发明授权、实用新型、外观设计)   kindcode
  @ApiPropertyOptional({ description: '', type: String })
  kindcode?: string;
}

/**
 * 软件著作权信息
 */
export class Scopr {
  //软件名称    name
  @ApiPropertyOptional({ description: '软件名称', type: String })
  name?: string;
}

/**
 *
 */
export class Telicense {
  //    scope
  @ApiPropertyOptional({ description: '', type: String })
  scope?: string;
}

/**
 * 招投标信息
 */
export class Tender {
  //招投标终止时间   enddate
  @ApiPropertyOptional({ description: '招投标终止时间', type: Date })
  enddate?: Date;

  //招投标项目角色(招标单位、投标单位、代理单位)   role
  @ApiPropertyOptional({
    description: '招投标项目角色(招标单位、投标单位、代理单位)',
    type: String,
  })
  role?: string;

  //招投标项目地区(省)    province
  @ApiPropertyOptional({ description: '招投标项目地区(省)', type: String })
  province?: string;

  //招投标项目地区(市)    city
  @ApiPropertyOptional({ description: '招投标项目地区(市)', type: String })
  city?: string;

  //招投标项目阶段(招标、投标、开标、评标、中标)   ifbprogress
  @ApiPropertyOptional({
    description: '招投标项目阶段(招标、投标、开标、评标、中标)',
    type: String,
  })
  ifbprogress?: string;

  //招投标项目预算   budgetvalue
  @ApiPropertyOptional({ description: '招投标项目预算', type: Number })
  budgetvalue?: number;

  //招投标发布时间   publishdate
  @ApiPropertyOptional({ description: '招投标发布时间', type: Date })
  publishdate?: Date;

  //招投标项目所属行业(市政设施、行政办公、能源化工、服务采购、政府采购、药品采购、建筑工程、医疗卫生、交通工程、水利水电、弱点安防、信息技术、其他)   industry
  @ApiPropertyOptional({
    description:
      '招投标项目所属行业(市政设施、行政办公、能源化工、服务采购、政府采购、药品采购、建筑工程、医疗卫生、交通工程、水利水电、弱点安防、信息技术、其他)',
    type: String,
  })
  industry?: string;

  //开标时间    opendate
  @ApiPropertyOptional({ description: '开标时间', type: Date })
  opendate?: Date;

  //招投标项目名称   title
  @ApiPropertyOptional({ description: '招投标项目名称', type: String })
  title?: string;
}

/**
 * 商标信息
 */
export class Trademark {
  //商标名   name
  @ApiPropertyOptional({ description: '商标名', type: String })
  name?: string;

  //商标专用权结束日期   validperiod
  @ApiPropertyOptional({ description: '商标专用权结束日期', type: Date })
  validperiod?: Date;
}

/**
 * 电话对象
 */
export class Tel {
  @ApiPropertyOptional({ description: '号码', type: String })
  t?: string;

  @ApiPropertyOptional({ description: '来源', type: String })
  s?: string;

  @ApiPropertyOptional({ description: '姓名', type: String })
  n?: string;

  @ApiPropertyOptional({ description: '职位', type: String })
  p?: string;

  @ApiPropertyOptional({ description: '1-可用(1:实号)， 2-不可用 (0: 空号、2:停机、4:沉默号、5:风险号), 3-未知 (3:库无 或其他未罗列出来的状态)', type: String })
  cs?: string;

  @ApiPropertyOptional({ description: '0-不是; 1-代记账号码; ', type: String })
  bat?: string;

  @ApiPropertyOptional({ description: '号码标签: kzz_suspected_legal-疑似法人，kzz_unique-客找找独有，kzz_recent_add-最近收录', type: String })
  tag?: string;
}

/**
 * Email对象
 */
export class Email {
  @ApiPropertyOptional({ description: 'email', type: String })
  e?: string;

  @ApiPropertyOptional({ description: '来源', type: String })
  s?: string;

  @ApiPropertyOptional({ description: '姓名', type: String })
  n?: string;

  @ApiPropertyOptional({ description: '职位', type: String })
  p?: string;
}

export class Contact {
  @ApiPropertyOptional({ description: '号码', type: Tel, isArray: true })
  tellist?: Tel[];

  @ApiPropertyOptional({ description: '邮箱', type: Email, isArray: true })
  emaillist?: Email[];
}

/**
 * tag对象
 */
export class TagInfo {
  @ApiPropertyOptional({ description: '', type: Number })
  t?: number;

  @ApiPropertyOptional({ description: '', type: String })
  n?: string;

  @ApiPropertyOptional({ description: '', type: String })
  s?: string;

  @ApiPropertyOptional({ description: '' })
  d?: any;
}
