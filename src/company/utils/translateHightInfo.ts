// import { BadParamsException, CommonExceptions } from '@kezhaozhao/search-utils';
import {
  DefaultHitReasonEntity,
  FIELD_NAME_REASON_MANUAL_MAP,
  FIELD_NAME_REASON_MAP,
  GROUP_OPER_MAP,
  HitReason,
  HitReasonFieldCollection,
  REG_EXP_CHARACTER_COMMAPAIR,
  REG_EXP_CHARACTER_FULLSTOPPAIR,
  REG_EXP_CHARACTER_SEMICOLONEPAIR,
  REG_EXP_CHARACTER_SEMICOLONPAIR,
  REG_EXP_CHINESE_CHARACTER,
  REG_EXP_EM_INNER,
  REG_EXP_EM_PAIR,
  REG_EXP_EM_SPECIAL_PAIR,
  REG_EXP_EM_TAG,
  SPLITTED_CHARACTER_MAP,
} from '../constant/eci-helper';
import { stringUtils } from '../../common/stringUtils';
import { cloneDeep, compact, filter, find, flatten, includes, map, orderBy, slice, sortBy, split, sum, sumBy, uniq } from 'lodash';
import { limitKeysJson } from '../constant/limit-keys';
import { characters } from '../../common/constants';

// /**
//  * 返回结果处理
//  * @param data
//  * @returns
//  */
// export const translateHightInfo = (data): any => {
//   try {
//     const res = {};
//     Object.keys(data).forEach((key) => {
//       let value = '';
//       if (Array.isArray(data[key])) {
//         value = data[key].join(',');
//       } else {
//         value = data[key] || '';
//       }
//       res[key] = value;
//     });
//     return res;
//   } catch (error) {
//     throw new BadParamsException(CommonExceptions.ES.Result.MappingError);
//   }
// };

const isStockCode = (key: string) => {
  return (
    stringUtils.isNumber(stringUtils.removeEm(key)) ||
    stringUtils.removeEm(key).endsWith('.HK') ||
    stringUtils.removeEm(key).endsWith('.NASDAQ') ||
    stringUtils.removeEm(key).endsWith('.NYSE') ||
    stringUtils.removeEm(key).endsWith('.SZ') ||
    stringUtils.removeEm(key).endsWith('.SH')
  );
};

const getOperHitReason = (name: string, econKind: string, operinfo?: string) => {
  let result = HitReasonFieldCollection.Oper;
  if (operinfo) {
    const operObj = JSON.parse(operinfo);
    if (GROUP_OPER_MAP.has(operObj.t)) {
      result = GROUP_OPER_MAP.get(operObj.t);
    }
  } else if (econKind && stringUtils.isContainsWords(econKind, ['合伙'])) {
    result = HitReasonFieldCollection.ExecutivePartner;
  } else if (name && stringUtils.isContainsWords(name, ['分公司'])) {
    result = HitReasonFieldCollection.Charger;
  }
  return result;
};

const processHighlight = (key: string, value: string[], highlight: any, searchKey: string) => {
  if (key === 'name') {
    if (highlight?.namesearch) {
      value = [...value, ...highlight.namesearch];
    }
    if (highlight?.name_tra) {
      value = [...value, ...highlight.name_tra];
    }
    if (highlight?.abbrname) {
      value = [...value, ...highlight.abbrname];
    }
  }
  value = value
    .map((item) =>
      item
        .replace(REG_EXP_CHARACTER_COMMAPAIR, '，')
        .replace(REG_EXP_CHARACTER_FULLSTOPPAIR, '。')
        .replace(REG_EXP_CHARACTER_SEMICOLONPAIR, '；')
        .replace(REG_EXP_CHARACTER_SEMICOLONEPAIR, ';'),
    )
    .filter((p) => stringUtils.isContainsEm(p));

  if (value.length) {
    const matchEmPairs = flatten(
      value.map((item) => {
        const replacedItem = item.replace(REG_EXP_EM_INNER, '');
        return REG_EXP_EM_INNER.exec(replacedItem) || REG_EXP_EM_SPECIAL_PAIR.exec(replacedItem);
      }),
    );
    const distinctHLValues: string[] = uniq(matchEmPairs.map((p: string) => p?.replace(REG_EXP_EM_TAG, '')));
    const defaultHitreasonObj: DefaultHitReasonEntity = {
      FieldName: key,
      Field: FIELD_NAME_REASON_MAP.get(key) || HitReasonFieldCollection.Default,
      FieldValue: value.map((p) =>
        p
          .replace(REG_EXP_CHARACTER_COMMAPAIR, '，')
          .replace(REG_EXP_CHARACTER_FULLSTOPPAIR, '。')
          .replace(REG_EXP_CHARACTER_SEMICOLONPAIR, '；')
          .replace(REG_EXP_CHARACTER_SEMICOLONEPAIR, ';'),
      ),
      Value: '',
      DistinctValues: distinctHLValues,
      DistinctValueLength: sumBy(distinctHLValues, (p) => p?.length),
      ExistingSameSearchKey:
        includes(distinctHLValues, searchKey) ||
        includes(
          distinctHLValues.map((p) => p?.toLowerCase()),
          searchKey.toLowerCase(),
        )
          ? 1
          : 0,
    };
    return defaultHitreasonObj;
  } else {
    return null;
  }
};

const processStockInfo = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string) => {
  let stockList = split(hl.FieldValue.join(''), /[;。]/);
  let filterEmItem = stockList.filter(
    (p) => p.indexOf('<em>') > -1 && (stringUtils.removeEm(p).indexOf(searchKey) > -1 || stringUtils.removeEm(p).indexOf(searchKey.toUpperCase()) > -1),
  );

  if (filterEmItem.length) {
    hl.Field = isStockCode(filterEmItem[0]) ? HitReasonFieldCollection.StockCode : HitReasonFieldCollection.StockName;
    hl.Value = filterEmItem[0];
  } else {
    // hl.Value = hl.FieldValue[0]
    stockList = split(hl.FieldValue.join(''), /[;。]/);
    filterEmItem = stockList.filter((p) => p.indexOf('<em>') > -1);

    if (filterEmItem.length) {
      hl.Field = isStockCode(filterEmItem[0]) ? HitReasonFieldCollection.StockCode : HitReasonFieldCollection.StockName;
      hl.Value = filterEmItem[0];
    }
  }
  finalHLs.push(hl);
};

const processCommaSeparatedList = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string, highlight: any) => {
  // 处理高亮部分，按照中文句号拆分，取每段em标签包含内容最长的一段
  const list = flatten(hl.FieldValue.map((item) => split(item, SPLITTED_CHARACTER_MAP.get(hl.FieldName)))).filter((p) => stringUtils.isContainsEm(p));
  const highlightMap = list.map((p: string) => {
    return {
      Context: p,
      InnerCount: sum((REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p))?.map((mc) => mc.replace(REG_EXP_EM_TAG, '').length)),
      ExistingSameSearchKey: p.replace(REG_EXP_EM_INNER, '').indexOf(`<em>${searchKey}</em>`) > -1 ? 1 : 0,
    };
  });
  highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
  hl.Value = highlightMap[0].Context;

  if (
    (hl.FieldName === 'featurelist2' || hl.FieldName === 'featurelist') &&
    ((highlight?.featurelist2 && highlight?.featurelist) ||
      (highlight?.featurelist2 && !highlight?.featurelist) ||
      (!highlight?.featurelist2 && highlight?.featurelist)) &&
    finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Trademark).length < 1
  ) {
    hl.Field = HitReasonFieldCollection.Trademark;
    finalHLs.push(hl);
  } else if (includes(['product', 'patentlist', 'contactnumber', 'wechat', 'email', 'app', 'ambiguity', 'invest'], hl.FieldName)) {
    hl.Field = FIELD_NAME_REASON_MANUAL_MAP.get(hl.FieldName);
    finalHLs.push(hl);
  }
};

const processWebsite = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string) => {
  // 处理高亮部分，按照中文句号拆分，取每段em标签包含内容最长的一段
  const websiteList = flatten(hl.FieldValue.map((item) => split(item, /[;。]/))).filter((p) => stringUtils.isContainsEm(p));
  const highlightMap = websiteList.map((p: string) => {
    return {
      Context: p,
      InnerCount: sum(
        REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p)
          ? (REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p))?.map((mc) => mc.replace(REG_EXP_EM_TAG, '').length)
          : [0],
      ),
      ExistingSameSearchKey: p.replace(REG_EXP_EM_INNER, '').indexOf(`<em>${searchKey}</em>`) > -1 ? 1 : 0,
    };
  });
  highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
  hl.Value = highlightMap[0].Context;
  hl.Field = HitReasonFieldCollection.Website;

  const replacedValue = hl.Value.replace(REG_EXP_EM_TAG, '').replace(REG_EXP_CHINESE_CHARACTER, '');
  if (!replacedValue || !stringUtils.isValidUrl(replacedValue)) {
    hl.Field = HitReasonFieldCollection.WebsiteName;
  }
  finalHLs.push(hl);
};

const processAddress = (hl: any, finalHLs: DefaultHitReasonEntity[], highlight: any, sortedHLs: any[]) => {
  if (highlight?.address2 && highlight?.address) {
    const addressHL = cloneDeep(find(sortedHLs, (p) => p.FieldName === 'address'));
    const address2HL = cloneDeep(find(sortedHLs, (p) => p.FieldName === 'address2'));
    if (address2HL?.ExistingSameSearchKey || (address2HL?.DistinctValueLength ?? 0) >= (addressHL?.DistinctValueLength ?? 0)) {
      address2HL.Field = HitReasonFieldCollection.ARAddress;
      address2HL.Value = address2HL.FieldValue[0];

      if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.ARAddress).length < 1) {
        finalHLs.push(address2HL);
      }
    } else if (addressHL?.ExistingSameSearchKey || (addressHL?.DistinctValueLength ?? 0) > (address2HL?.DistinctValueLength ?? 0)) {
      addressHL.Field = HitReasonFieldCollection.Address;
      addressHL.Value = addressHL.FieldValue[0];

      if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Address).length < 1) {
        finalHLs.push(addressHL);
      }
    }
  } else if (highlight?.address2 && !highlight?.address && hl.FieldName === 'address2') {
    hl.Field = HitReasonFieldCollection.ARAddress;
    hl.Value = hl.FieldValue[0];

    if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.ARAddress).length < 1) {
      finalHLs.push(hl);
    }
  } else if (!highlight?.address2 && highlight?.address && hl.FieldName === 'address') {
    hl.Field = HitReasonFieldCollection.Address;
    hl.Value = hl.FieldValue[0];

    if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Address).length < 1) {
      finalHLs.push(hl);
    }
  }
};

const processShortName = (hl: any, finalHLs: DefaultHitReasonEntity[], highlight: any, sortedHLs: any[]) => {
  hl.Field = HitReasonFieldCollection.PinYin;
  hl.Value = hl.FieldValue[0];

  if (highlight?.shortname && highlight?.['shortname.first']) {
    const snHL = cloneDeep(find(sortedHLs, (p) => p.FieldName === 'shortname'));
    const snfHL = cloneDeep(find(sortedHLs, (p) => p.FieldName === 'shortname.first'));
    if (snHL.DistinctValueLength >= snfHL.DistinctValueLength) {
      hl.Value = snHL.FieldValue[0];
    } else {
      hl.Value = snfHL.FieldValue[0];
    }
  }

  if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.PinYin).length < 1) {
    finalHLs.push(hl);
  }
};

const processOriginalName = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string, highlight: any) => {
  let highlightMap;
  hl.Field = HitReasonFieldCollection.OriginalName;
  if (hl.FieldValue.length === 1) {
    if (sum(map(hl.DistinctValues, (p) => p.length)) >= searchKey.length || !highlight?.['originalname.standard']) {
      hl.Value = hl.FieldValue[0];
    } else {
      hl.Value = highlight?.['originalname.standard'][0];
    }
  } else {
    highlightMap = hl.FieldValue.map((p: string) => {
      return {
        Context: p,
        InnerCount: sum((REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p)).map((mc) => mc.replace(REG_EXP_EM_TAG, '').length)),
        ExistingSameSearchKey: p.replace(REG_EXP_EM_INNER, '').indexOf(`<em>${searchKey}</em>`) > -1 ? 1 : 0,
      };
    });

    highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
    const filterOriginalName = highlightMap.filter((p) => p.InnerCount >= searchKey.length);
    if (filterOriginalName.length) {
      hl.Value = filterOriginalName[0].Context;
    } else if (highlight?.['originalname.standard']) {
      highlightMap = highlight?.['originalname.standard'].map((p: string) => {
        return {
          Context: p,
          InnerCount: sum((REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p)).map((mc) => mc.replace(REG_EXP_EM_TAG, '').length)),
          ExistingSameSearchKey: p.replace(REG_EXP_EM_INNER, '').indexOf(`<em>${searchKey}</em>`) > -1 ? 1 : 0,
        };
      });
      highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
      hl.Value = highlightMap[0].Context;
    } else {
      highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
      hl.Value = highlightMap[0].Context;
    }
  }

  // if (highlight?.id.startsWith('g')) {
  //   hl.Field = HitReasonFieldCollection.AliasName
  // }
  finalHLs.push(hl);
};

const processOriginalNameStandard = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string, highlight: any) => {
  let highlightMap;
  hl.Field = HitReasonFieldCollection.OriginalName;
  if (!highlight?.originalname) {
    highlightMap = highlight?.['originalname.standard'].map((p: string) => {
      return {
        Context: p,
        InnerCount: sum((REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p)).map((mc) => mc.replace(REG_EXP_EM_TAG, '').length)),
        ExistingSameSearchKey: p.replace(REG_EXP_EM_INNER, '').indexOf(`<em>${searchKey}</em>`) > -1 ? 1 : 0,
      };
    });
    highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
    hl.Value = highlightMap[0].Context;

    // if (highlight?.id.startsWith('g')) {
    //   hl.Field = HitReasonFieldCollection.AliasName
    // }
    finalHLs.push(hl);
  }
};

const processPersonSearch = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string, highlight: any) => {
  const splittedSearchKey = split(searchKey, ' ');
  if (includes(splittedSearchKey, highlight?.opername)) {
    hl.Field = HitReasonFieldCollection.Oper;
    hl.Value = `<em>${highlight?.opername}</em>`;
  } else {
    let i: number;
    for (i = 0; i < splittedSearchKey.length; i++) {
      if (highlight?.promoterlist2) {
        let j: number;
        for (j = 0; j < highlight?.promoterlist2.length; j++) {
          if (stringUtils.removeEm(highlight?.promoterlist2[j]).toLowerCase().indexOf(splittedSearchKey[i].toLowerCase()) > -1) {
            hl.Field = HitReasonFieldCollection.Invester;
            hl.Value = highlight?.promoterlist2[j];
            break;
          }
        }
        if (hl.Field) {
          break;
        }
      }
    }
    if (!hl.Field) {
      hl.Field = HitReasonFieldCollection.Employee;
      hl.Value = (REG_EXP_EM_PAIR.exec(hl.FieldValue[0]) || REG_EXP_EM_SPECIAL_PAIR.exec(hl.FieldValue[0]))[0];
    }
  }
  if (hl.Field !== HitReasonFieldCollection.Invester || finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Invester).length < 1) {
    finalHLs.push(hl);
  }
};

const processDefault = (hl: any, finalHLs: DefaultHitReasonEntity[], searchKey: string) => {
  const highlightMap = hl.FieldValue.map((p: string) => {
    return {
      Context: p,
      InnerCount: sum((REG_EXP_EM_PAIR.exec(p) || REG_EXP_EM_SPECIAL_PAIR.exec(p)).map((mc) => mc.replace(REG_EXP_EM_TAG, '').length)),
      ExistingSameSearchKey: p.replace(REG_EXP_EM_INNER, '').indexOf(`<em>${searchKey}</em>`) > -1 ? 1 : 0,
    };
  });
  highlightMap.sort((a, b) => b.ExistingSameSearchKey - a.ExistingSameSearchKey || b.InnerCount - a.InnerCount);
  hl.Value = highlightMap[0].Context;
  if (hl.FieldName === 'promoterlist2' || hl.FieldName === 'promoterlist.text') {
    hl.Field = HitReasonFieldCollection.Invester;
  } else if (hl.FieldName === 'scope_rp' || hl.FieldName === 'scope') {
    hl.Field = HitReasonFieldCollection.Scope;
    let k: number;
    for (k = 0; k < characters.length; k++) {
      if (hl.Value.startsWith(characters[k])) {
        hl.Value = hl.Value.substr(1, hl.Value.length);
        break;
      }
    }
  } else if (hl.FieldName === 'originalname') {
    hl.Field = HitReasonFieldCollection.OriginalName;
  } else if (hl.FieldName === 'bondcode') {
    hl.Field = HitReasonFieldCollection.BondCode;
  } else if (hl.FieldName === 'bondname') {
    hl.Field = HitReasonFieldCollection.BondName;
  }
  if (hl.Field !== HitReasonFieldCollection.Invester || finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Invester).length < 1) {
    finalHLs.push(hl);
  }
};

export const getHitReason = (source: any, highlight: any, searchKey: string, hitReasonCount: number): HitReason[] => {
  const processedHLs = Object.entries(highlight)
    .map(([key, value]: [string, string[]]) => {
      return processHighlight(key, value, highlight, searchKey);
    })
    .filter((p) => p);

  const sortedHLs = sortBy(processedHLs, (p) => -p.DistinctValues.length);
  const finalHLs: DefaultHitReasonEntity[] = [];
  sortedHLs.forEach((hl) => {
    if (hl.Field === HitReasonFieldCollection.Default) {
      // console.log('------1611');
      switch (hl.FieldName) {
        case 'stockinfo': {
          processStockInfo(hl, finalHLs, searchKey);
          break;
        }
        case 'featurelist2':
        case 'featurelist':
        case 'product':
        case 'patentlist':
        case 'contactnumber':
        case 'wechat':
        case 'email':
        case 'app':
        case 'invest':
        case 'ambiguity':
          processCommaSeparatedList(hl, finalHLs, searchKey, highlight);
          break;
        case 'website':
          processWebsite(hl, finalHLs, searchKey);
          break;
        case 'name': {
          // console.log('--name------');
          // console.log();
          hl.Field = HitReasonFieldCollection.Name;
          const matchEmPairs = flatten(
            highlight?.name.map((item) => {
              const replacedItem = item.replace(REG_EXP_EM_INNER, '');
              // console.log('replacedItem:', replacedItem);
              return REG_EXP_EM_INNER.exec(replacedItem) || REG_EXP_EM_SPECIAL_PAIR.exec(replacedItem);
            }),
          );
          // console.log('matchEmPairs:', matchEmPairs);
          const distinctHLValues: string[] = compact(uniq(matchEmPairs.map((p: string) => p?.replace(REG_EXP_EM_TAG, ''))));
          // console.log('distinctHLValues:', distinctHLValues);

          const innerContextLength = sum(map(distinctHLValues, (p) => p.length));
          const traditionalItem = find(sortedHLs, (p) => p.FieldName === 'name_tra');
          // console.log('innerContextLength:', innerContextLength);
          // console.log('traditionalItem:', traditionalItem);
          // console.log('searchKey.length:', searchKey.length);
          if (!traditionalItem || innerContextLength >= traditionalItem.DistinctValueLength) {
            // console.log('hl.FieldValue[0]:', hl.FieldValue[0]);
            // console.log('highlight?.namesearch:', highlight?.namesearch);
            hl.Value = hl.FieldValue[0];
            // // "清博"关键字导致的分词异常
            // if (innerContextLength >= searchKey.length) {
            //   hl.Value = hl.FieldValue[0];
            // } else {
            //   hl.Value = highlight?.namesearch ? highlight?.namesearch[0] : hl.FieldValue[0];
            // }
          } else if (traditionalItem.DistinctValueLength >= searchKey.length) {
            hl.Value = traditionalItem.FieldValue[0];
          } else {
            hl.Value = highlight?.namesearch ? highlight?.namesearch[0] : traditionalItem.FieldValue[0];
          }

          // console.log('hl.value:', hl.Value);
          // updated by chuanliang on 8 Feb 2021 for abbrname 高亮
          if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Name).length < 1) {
            finalHLs.push(hl);
          }
          break;
        }
        case 'name_tra':
          hl.Field = HitReasonFieldCollection.Name;
          if (!highlight?.name && !highlight?.namesearch) {
            if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Name).length < 1) {
              hl.Value = hl.FieldValue[0];
              finalHLs.push(hl);
            }
          }
          break;
        case 'namesearch':
          hl.Field = HitReasonFieldCollection.Name;
          if (!highlight?.name && !highlight?.name_tra) {
            hl.Value = hl.FieldValue[0];
            finalHLs.push(hl);
          } else if (highlight?.name_tra) {
            const traHL = find(sortedHLs, (p) => p.FieldName === 'name_tra');
            if (traHL && traHL.DistinctValueLength >= hl.DistinctValueLength) {
              hl.Value = traHL.FieldValue[0];
            } else {
              hl.Value = hl.FieldValue[0];
            }

            if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Name).length < 1) {
              finalHLs.push(hl);
            }
          }
          break;
        case 'abbrname':
          hl.Field = HitReasonFieldCollection.Name;
          // added by chuanliang on 8 Feb 2021 for abbrname 高亮
          if (finalHLs.filter((p) => p.Field === HitReasonFieldCollection.Name).length < 1) {
            hl.Value = hl.FieldValue[0];
            finalHLs.push(hl);
          }
          break;
        case 'address':
        case 'address2': {
          processAddress(hl, finalHLs, highlight, sortedHLs);
          break;
        }
        case 'shortname':
        case 'shortname.first':
          processShortName(hl, finalHLs, highlight, sortedHLs);
          break;
        case 'originalname': {
          processOriginalName(hl, finalHLs, searchKey, highlight);
          break;
        }
        case 'originalname.standard':
          processOriginalNameStandard(hl, finalHLs, searchKey, highlight);
          break;
        case 'personsearch':
          processPersonSearch(hl, finalHLs, searchKey, highlight);
          break;
        default:
          processDefault(hl, finalHLs, searchKey);
          break;
      }
    } else if (hl.Field === HitReasonFieldCollection.OriginalName && source?.id.startsWith('g')) {
      hl.Field = HitReasonFieldCollection.AliasName;
      finalHLs.push(hl);
    } else if (hl.Field === HitReasonFieldCollection.Oper) {
      hl.Field = getOperHitReason(source?.opername, source?.econkind, source?.operinfo);
      hl.Value = hl.FieldValue[0];
      finalHLs.push(hl);
    } else {
      hl.Value = hl.FieldValue[0];

      if (finalHLs.filter((p) => p.Field === hl.Field).length < 1) {
        finalHLs.push(hl);
      }
      // finalHLs.push(hl)
    }
  });

  // 排序逻辑
  const ordered1st = orderBy(finalHLs, ['ExistingSameSearchKey', 'DistinctValueLength'], ['desc', 'desc']);
  // console.log('------1711');
  // console.log('ordered1st:', ordered1st);

  const existingSameSearchKeyList = filter(ordered1st, (p) => p.ExistingSameSearchKey === 1);
  // console.log('------1712');
  // console.log('existingSameSearchKeyList:', existingSameSearchKeyList);
  const sortedIndexArray = [
    'stockinfo',
    'product',
    'name_alias',
    'abbrname',
    'name',
    'opername',
    'featurelist2',
    'featurelist',
    'invest',
    'app',
    'creditcode',
    'regno',
    'contactnumber',
    'website',
    'email',
  ];
  existingSameSearchKeyList.sort((a, b) => {
    return (
      (sortedIndexArray.indexOf(a.FieldName) < 0 ? 99 : sortedIndexArray.indexOf(a.FieldName)) -
      (sortedIndexArray.indexOf(b.FieldName) < 0 ? 99 : sortedIndexArray.indexOf(b.FieldName))
    );
  });
  // console.log('------1713');
  // console.log('existingSameSearchKeyList:', existingSameSearchKeyList);
  const nonExistingSameSearchKeyList = ordered1st.filter((p) => p.ExistingSameSearchKey === 0);
  nonExistingSameSearchKeyList.sort((a, b) => {
    return (
      b.DistinctValueLength - a.DistinctValueLength ||
      (sortedIndexArray.indexOf(a.FieldName) < 0 ? 99 : sortedIndexArray.indexOf(a.FieldName)) -
        (sortedIndexArray.indexOf(b.FieldName) < 0 ? 99 : sortedIndexArray.indexOf(b.FieldName))
    );
  });
  // console.log('------1714');
  // console.log('nonExistingSameSearchKeyList:', nonExistingSameSearchKeyList);
  const concattedList = [...existingSameSearchKeyList, ...nonExistingSameSearchKeyList];

  // console.log('------1811');
  // console.log('concattedList:', concattedList);
  // return concattedList
  const slicedHRs = slice(concattedList, 0, hitReasonCount)?.map((p) => {
    const replacedValue = p.Value.replace(REG_EXP_EM_INNER, '');
    const distinctdValue = uniq(
      (REG_EXP_EM_PAIR.exec(replacedValue.replace(REG_EXP_EM_INNER, '')) || REG_EXP_EM_SPECIAL_PAIR.exec(replacedValue.replace(REG_EXP_EM_INNER, '')))?.map(
        (mc) => mc.replace(REG_EXP_EM_TAG, ''),
      ),
    );

    return {
      Field: p.Field,
      Value: replacedValue,
      DistinctValues: distinctdValue,
      DistinctValueLength: sum(distinctdValue.map((i) => i.length)),
    };
  });

  // console.log('------1911');
  // console.log('slicedHRs:', slicedHRs);
  if (slicedHRs.length > 1) {
    let i: number;
    for (i = slicedHRs.length - 1; i >= 1; i--) {
      if (slicedHRs[i].DistinctValueLength >= 1) {
        if (slicedHRs[i].DistinctValueLength === 1 || slicedHRs[i].DistinctValues.every((p) => includes(limitKeysJson.LimitKeys, p))) {
          slicedHRs.splice(i, 1);
        }
      }
    }
  }

  // console.log('------2011');
  // console.log('slicedHRs:', slicedHRs);
  return slicedHRs.length
    ? slicedHRs.map((p) => {
        let value = p.Value;
        if (p.Field === HitReasonFieldCollection.Name) {
          value = p.Value.split(',')[0];
        }
        return {
          Field: p.Field,
          Value: value,
        };
      })
    : null;
};
