import { difference, isArray, isUndefined, keys } from 'lodash';
import { Email, SearchFilter, Tel, KysCompanyResponseDetails } from '../model';
import { FuzzyFields, KysFilterMap, KysSearchFieldMapping, KysSearchParams } from '../constant/constant.kys';
// import { BadParamsException, CommonExceptions, CommonUtils, DateRangeRelative } from '@kezhaozhao/search-utils';
import { CommonExceptions } from '../../common/exceptions/exceptionConstants';

import moment = require('moment');
import { BadParamsException } from '../../common/exceptions/BadParamsException';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { RequestUtils } from '@kezhaozhao/qcc-common-utils';

interface ESBoolShould {
  should: object[];
}

export const kysGenerateSearchIndexArray = (searchIndex: string[], searchKey: string) => {
  const musts: object[] = [];

  const parentIndex: string[] = searchIndex;

  if (parentIndex && parentIndex.length > 0) {
    //合并index
    const phraseIndex: string[] = [];
    const bestFieldsIndex: string[] = [];
    parentIndex.forEach((i) => {
      bestFieldsIndex.push(...KysSearchFieldMapping[i]);
      if (FuzzyFields[i]?.length) {
        phraseIndex.push(...FuzzyFields[i]);
      }
    });
    if (phraseIndex.length) {
      const must = {
        multi_match: {
          fields: phraseIndex,
          query: searchKey,
          type: 'phrase',
          operator: 'AND',
        },
      };
      musts.push(must);
    }
    if (bestFieldsIndex.length) {
      const must = {
        multi_match: {
          fields: bestFieldsIndex,
          query: searchKey,
          type: 'best_fields',
          operator: 'AND',
        },
      };
      musts.push(must);
    }
  }

  return { bool: { should: musts, minimum_should_match: 1 } };
};

/**
 * 格式化处理es数据
 * @param data
 * @param decryption true 人员姓名脱敏
 */
export const GetESDeatils = (data, decryption = false): KysCompanyResponseDetails => {
  try {
    const res: KysCompanyResponseDetails = new KysCompanyResponseDetails();
    Object.keys(data._source).forEach((key) => {
      const value = data._source[key];

      if (!['patent'].includes(key)) {
        switch (key) {
          case 'website': {
            res.website = [''];
            if (data._source?.flag?.includes('GW') && value?.[0]) {
              res.website = value;
            }
            break;
          }
          // case 'contactnumber': {
          //   res.contactnumber = value?.split('。');
          //   break;
          // }
          // case 'email': {
          //   res.email = value;
          //   break;
          // }
          case 'latestchangetype': {
            res.latestchangetype = value?.length ? value?.join(',') : '';
            break;
          }
          case 'tellist': {
            const tels = value ? (value as Tel[]) : [];
            if (tels?.length) {
              tels.forEach((tel) => {
                if (tel?.n && decryption) {
                  tel.n = tel.n[0] + '**';
                }
                if (isUndefined(tel.cs)) {
                  tel.cs = '3';
                } else if (tel.cs !== '1' && tel.cs !== '3') {
                  tel.cs = '2';
                }
              });
            }
            res.tellist = tels;
            break;
          }
          case 'tellistkzz': {
            const tels: Tel[] = value ? (JSON.parse(value) as Tel[]) : [];
            if (tels?.length) {
              tels.forEach((tel) => {
                if (tel?.n && decryption) {
                  tel.n = tel.n[0] + '**';
                }
                if (isUndefined(tel.cs)) {
                  tel.cs = '3';
                } else if (tel.cs !== '1' && tel.cs !== '3') {
                  tel.cs = '2';
                }
              });
            }
            res.tellistkzz = tels;
            break;
          }
          case 'emaillist': {
            const emails = value ? (JSON.parse(value) as Email[]) : [];
            if (emails?.length) {
              emails.forEach((email) => {
                if (email?.n && decryption) {
                  email.n = email.n[0] + '**';
                }
              });
            }
            res.emaillist = emails;
            break;
          }
          case 'commonlist':
          case 'operinfo':
          case 'trademark':
          case 'tender':
          case 'scopr':
          case 'patent':
          case 'license':
          case 'exception':
          case 'customs':
          case 'cret':
          case 'copr':
          case 'chinacourt':
          case 'case': {
            res[key] = value ? JSON.parse(value) : value;
            break;
          }
          case 'insuredcount': {
            if (value >= 0) {
              res[key] = value;
            } else {
              res[key] = '-';
            }
            break;
          }
          case 'introduction': {
            if (value.includes('<')) {
              let introduction = value.replace(/<\/?[^>]*>/g, ''); //去除HTML Tag
              introduction = introduction.replace(/&npsp;/gi, ''); //去掉npsp
              res.introduction = introduction.trim();
            } else {
              res.introduction = value;
            }
            break;
          }
          case 'hasimage': {
            res.hasimage = value ? 1 : 0;
            break;
          }
          default: {
            res[key] = value;
          }
        }
      }
    });
    if (data.highlight) {
      res['highlight'] = data.highlight;
    }
    return res;
  } catch (error) {
    throw new BadParamsException(CommonExceptions.ES.Result.MappingError);
  }
};

export const processIdsFilter = (filterBody: SearchFilter, filter: object[]): object[] => {
  // 根据companyIDs 查询
  if (filterBody?.ids?.length) {
    const ids = { values: filterBody.ids };
    filter.push({ ids });
  }
  return filter;
};

export const processPslFilter = (filterBody: SearchFilter, filter: object[], key: string): object[] => {
  if (filterBody[key]) {
    filter.push({
      bool: {
        should: [{ terms: { personsearch: filterBody[key] } }, { terms: { opername: filterBody[key] } }],
        minimum_should_match: 1,
      },
    });
  }
  return filter;
};

export const processLstFilter = (filterBody: SearchFilter, filter: object[]): object[] => {
  // 上市信息 listingstatus
  const value = filterBody.lst;
  if (value) {
    if (!isArray(value)) {
      throw new BadParamsException(CommonExceptions.BadParams.Common);
    }
    const terms = {};
    if (value.includes('-1')) {
      terms[KysFilterMap.lst] = difference(
        keys(KysSearchParams.listingInfo),
        value.map((v) => v + ''),
      );
      filter.push({ bool: { must_not: { terms } } });
    } else {
      terms[KysFilterMap.lst] = value;
      filter.push({ terms });
    }
  }
  return filter;
};

export const processRuFilter = (filterBody: SearchFilter, filter: object[]): object[] => {
  const value = filterBody.ru;
  if (value) {
    if (!isArray(value)) {
      throw new BadParamsException(CommonExceptions.BadParams.Common);
    }
    const terms = {};
    if (value.includes('OTHER')) {
      terms[KysFilterMap.ru] = difference(['CNY', 'USD'], value);
      filter.push({ bool: { must_not: { terms } } });
    } else {
      terms[KysFilterMap.ru] = value;
      filter.push({ terms });
    }
  }
  return filter;
};

export const processGxqyFilter = (filterBody: SearchFilter, filter: object[]): object[] => {
  if (filterBody.gxqy) {
    const term = {};
    term[KysFilterMap.gxqy] = '高新技术企业';
    if (filterBody.gxqy == 'Y') {
      filter.push({ term });
    } else {
      filter.push({ bool: { must_not: { term } } });
    }
  }
  return filter;
};

export const processIcFilter = (filterBody: SearchFilter, filter: object[], key: string): object[] => {
  //参保人数
  if (filterBody[key]) {
    const rShould: ESBoolShould = {
      should: [],
    };
    // min - 最小值，max - 最大值
    filterBody[key].forEach((o) => {
      const range = {};
      if (o.min === o.max && o.max === 0) {
        range[KysFilterMap[key]] = {
          gte: o.min,
          lt: o.max,
        };
      } else {
        range[KysFilterMap[key]] = {
          gte: o.min || 0,
          lt: o.max || undefined,
        };
      }

      rShould.should.push({ range });
    });
    if (rShould.should.length) {
      rShould['minimum_should_match'] = 1;
    }
    filter.push({ bool: rShould });
  }
  return filter;
};

export const processSdFilter = (filterBody: SearchFilter, filter: object[]): object[] => {
  if (filterBody?.sd) {
    const value: DateRangeRelative[] = [];
    if (isArray(filterBody.sd)) {
      value.push(...(filterBody.sd as unknown as DateRangeRelative[]));
    } else {
      value.push(filterBody.sd);
    }
    const rShould: ESBoolShould = {
      should: [],
    };
    value.forEach((dr) => {
      const dateR = RequestUtils.ProcessDRR(dr, 'YYYYMMDD');
      if (!dateR.end) {
        dateR.end = moment().format('YYYYMMDD');
      }
      if (!dateR.start) {
        dateR.start = moment(1000).format('YYYYMMDD');
      }
      const dateRange = {
        time_zone: '+08:00',
        gte: dateR.start,
        lte: dateR.end,
      };
      const range = {};
      range[KysFilterMap.sd] = dateRange;
      if (!dateR.end) {
        dateR.end = moment().format('YYYY-MM-DD HH:mm:ss');
      }
      if (!dateR.start) {
        dateR.start = moment(1000).format('YYYY-MM-DD HH:mm:ss');
      }
      rShould.should.push({ range });
    });
    if (rShould.should.length) {
      rShould['minimum_should_match'] = 1;
    }
    console.log();

    filter.push({ bool: rShould });
  }
  return filter;
};

export const processRegionFilter = (filterBody: SearchFilter, filter: object[]): object[] => {
  if (filterBody.r) {
    const rShould: ESBoolShould = {
      should: [],
    };
    filterBody.r.forEach((o) => {
      const terms = [];
      if (o.pr) {
        const term = {};
        term[KysFilterMap.r.pr] = o.pr;
        terms.push({ term });
      }
      if (o.dt) {
        const term = {};
        term[KysFilterMap.r.ac] = o.dt;
        terms.push({ term });
      } else if (o.ct) {
        const term = {};
        term[KysFilterMap.r.ac] = o.ct;
        terms.push({ term });
      }

      if (terms && terms.length > 0) {
        rShould.should.push({ bool: { filter: terms } });
      }
    });
    if (rShould.should.length) {
      rShould['minimum_should_match'] = 1;
    }
    filter.push({ bool: rShould });
  }
  return filter;
};

// 根据参数生成es的Filter条件
export const kysGenerateFilterArray = (filterBody: SearchFilter, mapType = 0): object[] => {
  const filter: object[] = [];
  try {
    Object.keys(filterBody).forEach((key) => {
      switch (key) {
        // 金融机构
        case 'termsFlag':
          if (filterBody[key].length) {
            const terms = {};
            terms[KysFilterMap[key]] = filterBody[key];
            filter.push({ terms });
          }
          break;
        case 'ids':
          processIdsFilter(filterBody, filter);
          break;
        case 'psl': // personsearch
          processPslFilter(filterBody, filter, key);
          break;
        case 'sc': //企业经营状态 statuscode
        case 'ekc': //企业类型  econkind(企业类型描述)/econkindcode(企业类型code)
        case 'ot': //组织机构类型 type  0: 大陆公司, 1: 社会组织, 3: 香港公司...
        case 'tag': {
          //新兴行业  tag
          if (filterBody[key]) {
            const terms = {};
            terms[KysFilterMap[key]] = filterBody[key];
            filter.push({ terms });
          }
          break;
        }
        case 'lst':
          processLstFilter(filterBody, filter);
          break;
        //registerunit 注册资本类型，USD,CNY,OTHER
        case 'ru': {
          processRuFilter(filterBody, filter);
          break;
        }
        //是否高新企业 tag
        case 'gxqy': {
          processGxqyFilter(filterBody, filter);
          break;
        }
        case 'rca': //注册资本区间  registcapi(注册资本描述)/registcapiamount(注册资本数值)
        case 'ic': {
          processIcFilter(filterBody, filter, key);
          break;
        }

        //成立时间 startdateyear/startdatecode
        case 'sd': {
          processSdFilter(filterBody, filter);
          break;
        }

        // case 'zjasc': // assetshierarchy 资产规模
        // case 'zjrhc': // revenuehierarchy 营收水平万元
        // case 'zjphc': {
        //   // profithierarchy 利润水平万元
        //   if (filterBody[key]) {
        //     const terms = {};
        //     terms[KysFilterMap[key]] = filterBody[key].join(',').split(',');
        //     filter.push({ terms });
        //   }
        //   break;
        // }

        // case 'ltl': {
        //   // latesttaxlevel  最新纳税信用等级 A 、N_A
        //   const value = filterBody.ltl;
        //   if (value) {
        //     const match = {};
        //     match[KysFilterMap.ltl] = value.startsWith('N_') ? '' : value;
        //     filter.push({ match });
        //   }

        //   break;
        // }

        // case 'qccl': {
        //   // 企查查认证等级   qccauthenticationlevel * QCCL: 有认证;  N_QCCL:无认证;
        //   const value = filterBody.qccl;
        //   if (value) {
        //     const terms = {};
        //     // -1未认证, 0普通认证、 1高级认证、 2超级认证、 3联合认证',
        //     if (value === 'N_QCCL') {
        //       terms[KysFilterMap.qccl] = [-1];
        //     } else {
        //       terms[KysFilterMap.qccl] = [0, 1, 2, 3];
        //     }
        //     filter.push({ terms });
        //   }
        //   break;
        // }

        // //变更日期(工商信息) updatedate
        case 'ud': {
          if (filterBody.ud) {
            const dateR = RequestUtils.ProcessDRR(filterBody.ud);
            if (!dateR.end) {
              dateR.end = moment().format('YYYY-MM-DD HH:mm:ss');
            }
            if (!dateR.start) {
              dateR.start = moment(1000).format('YYYY-MM-DD HH:mm:ss');
            }
            const dateRange = {
              gte: Math.floor(moment(dateR.start).valueOf() / 1000),
              lte: Math.floor(moment(dateR.end).valueOf() / 1000),
            };
            const range = {};
            range[KysFilterMap.ud] = dateRange;

            filter.push({ range });
          }
          break;
        }

        //region 行政区域  { pr: 省份代码 province, ac: 市/区县代码  areacode }
        case 'r': {
          processRegionFilter(filterBody, filter);
          break;
        }
        //行业分类 industry,subInd
        case 'i': {
          if (filterBody.i) {
            const rShould: ESBoolShould = {
              should: [],
            };

            filterBody.i.forEach((o) => {
              const terms = [];

              if (o.i1) {
                const term = {};
                term[KysFilterMap.i.ind] = o.i1;
                terms.push({ term });
              }
              if (o.i4) {
                const term = {};
                term[KysFilterMap.i.sub] = o.i4;
                terms.push({ term });
              } else if (o.i3) {
                const term = {};
                term[KysFilterMap.i.sub] = o.i3;
                terms.push({ term });
              } else if (o.i2) {
                const term = {};
                term[KysFilterMap.i.sub] = o.i2;
                terms.push({ term });
              }

              if (terms && terms.length > 0) {
                rShould.should.push({ bool: { filter: terms } });
              }
            });
            if (rShould.should.length) {
              rShould['minimum_should_match'] = 1;
            }
            filter.push({ bool: rShould });
          }
          break;
        }

        //企业联系方式
        case 'ci': {
          if (filterBody.ci) {
            if (filterBody.ci.ht) {
              const term = {};
              term[KysFilterMap.ci] = 'T';
              filter.push({ term });
            }
            if (filterBody.ci.hm) {
              const term = {};
              term[KysFilterMap.ci] = 'MN';
              filter.push({ term });
            }
            if (filterBody.ci.he) {
              const term = {};
              term[KysFilterMap.ci] = 'E';
              filter.push({ term });
            }
          }
          break;
        }

        //查询 flag
        case 'flag': {
          if (filterBody.flag) {
            const mustFlags = filterBody.flag.filter((o) => o.indexOf('N_') < 0);
            if (mustFlags && mustFlags.length > 0) {
              mustFlags.forEach((o) => {
                const term = {};
                term[KysFilterMap.flag] = o;
                filter.push({ term });
              });
            }

            const mustNotFlags = filterBody.flag.filter((o) => o.indexOf('N_') >= 0);
            if (mustNotFlags && mustNotFlags.length > 0) {
              const terms = mustNotFlags.map((o) => {
                const term = {};
                term[KysFilterMap.flag] = o.replace('N_', '');
                return { term };
              });
              filter.push({ bool: { must_not: terms } });
            }
          }
          break;
        }

        // 圆形范围查询
        case 'distance': {
          if (filterBody?.distance) {
            // "geo_distance": {
            //   "distance": "1km",
            //   "location": {
            //       "lat": "31.31297793454742",
            //       "lon": "120.78272609849135"
            //   }
            // }
            const geo_distance = {
              distance: filterBody.distance?.distance + filterBody.distance?.unit,
              location: filterBody.distance?.point,
            };

            filter.push({ geo_distance });

            // 年报地址 reportlocation 没有数据
            // const geo_distance2 = {
            //   distance: filterBody.distance?.distance + filterBody.distance?.unit,
            //   reportlocation: filterBody.distance?.point,
            // };

            // switch (mapType) {
            //   case 1:
            //     filter.push({ geo_distance: geo_distance2 });
            //     break;
            //   case 2:
            //     filter.push({ geo_distance });
            //     break;
            //   default:
            //     const rShould: ESBoolShould = {
            //       should: [],
            //     };

            //     rShould.should.push({ geo_distance }, { geo_distance: geo_distance2 });
            //     if (rShould.should.length) {
            //       rShould['minimum_should_match'] = 1;
            //     }

            //     filter.push({ bool: rShould });
            //     break;
            // }
          }
          break;
        }
        // 不规则图形范围查询
        case 'polygon': {
          //   "geo_polygon": {
          //     "location": {
          //         "points": [
          //             {
          //                 "lon": 120.521486,
          //                 "lat": 31.368227
          //             },
          //             {
          //                 "lon": 120.520336,
          //                 "lat": 31.247778
          //             },
          //             {
          //                 "lon": 120.677863,
          //                 "lat": 31.254693
          //             },
          //             {
          //                 "lon": 120.670389,
          //                 "lat": 31.360826
          //             },
          //             {
          //                 "lon": 120.520911,
          //                 "lat": 31.368227
          //             }
          //         ]
          //     }
          // }
          if (filterBody?.polygon) {
            // const rShould: ESBoolShould = {
            //   should: []
            // };

            const geo_polygon = {
              location: {
                points: filterBody?.polygon,
              },
            };
            filter.push({ geo_polygon });

            // const geo_polygon2 = {
            //   reportlocation: {
            //     points: filterBody?.polygon,
            //   },
            // };

            // switch (mapType) {
            //   case 1:
            //     filter.push({ geo_polygon: geo_polygon2 });
            //     break;
            //   case 2:
            //     filter.push({ geo_polygon });
            //     break;
            //   default:
            //     const rShould: ESBoolShould = {
            //       should: [],
            //     };

            //     rShould.should.push({ geo_polygon }, { geo_polygon: geo_polygon2 });
            //     if (rShould.should.length) {
            //       rShould['minimum_should_match'] = 1;
            //     }

            //     filter.push({ bool: rShould });
            //     break;
            // }
            // rShould.should.push({ geo_polygon }, { geo_polygon: geo_polygon2 });
            // if (rShould.should.length) {
            //   rShould['minimum_should_match'] = 1;
            // }

            // filter.push({ geo_polygon });
          }
          break;
        }
      }
    });
    return filter;
  } catch (error) {
    throw new Error(`generateFilterArray ${error}`);
  }
};
