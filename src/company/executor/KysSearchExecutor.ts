import { ESBool } from '../model';
import { compact, isEmpty, split } from 'lodash';
import { KysCompanySearchRequest } from '../model/KysCompanySearchRequest';
import { GetESDeatils, kysGenerateFilterArray, kysGenerateSearchIndexArray } from '../utils/kys-utils';
import { getHitReason } from '../utils/translateHightInfo';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { EsSearchBase } from './EsSearchBase';

export class KysSearchExecutor extends EsSearchBase<KysCompanySearchRequest> {
  private kysLogger: Logger = QccLogger.getLogger(KysSearchExecutor.name);
  private rescoreFilters: object[] = [];
  private noScore = true;

  public convertQuery(searchBody: KysCompanySearchRequest): object {
    // const boolContext = this.transfer(searchBody.searchObject, 0);

    const boolContext: ESBool = {
      must: [],
      filter: [],
      should: [],
      must_not: [],
    };

    const searchKeys: string[] = compact(split(searchBody.searchKey, ' '));
    if (searchKeys && searchKeys?.length > 0) {
      const must = searchKeys.map((searchKey) => kysGenerateSearchIndexArray(searchBody.searchIndex, searchKey));
      boolContext.must.push(...must);
    }

    if (searchBody.filter) {
      const filterArray = kysGenerateFilterArray(searchBody.filter);
      boolContext.filter.push(...filterArray);
    }

    if (!searchBody?.includeInvalid) {
      boolContext.filter.push({ term: { isvalid: 1 } });
    }

    // 过滤已注销的企业
    // boolContext.must_not.push({ term: { statuscode: 99 } });
    if (boolContext?.should?.length) {
      boolContext['minimum_should_match'] = 1;
    }
    return { bool: boolContext };
  }

  protected getRescoreQuery(): any {
    if (this.noScore || isEmpty(this.param?.searchKey)) {
      return null;
    }
    return {
      window_size: '2000',
      query: {
        score_mode: 'multiply',
        rescore_query: {
          function_score: {
            functions: [
              {
                field_value_factor: {
                  field: 'weight',
                  factor: 1.259,
                  modifier: 'none',
                },
              },
              { filter: this.rescoreFilters, weight: 1.5 },
            ],
            score_mode: 'multiply',
            max_boost: 3.4028235e38,
            boost: 1,
          },
        },
      },
    };
  }

  protected getQuerySource() {
    const includeFields = this.param.includeFields;
    return { includes: includeFields };
  }

  protected getBodyAggregation() {
    const aggs = {};
    this.param?.aggFields?.forEach((field) => {
      aggs[field] = { terms: { field } };
    });
    return aggs;
  }

  protected generateQuery() {
    const searchBody = this.param;
    return this.convertQuery(searchBody);
  }

  protected getBodyHighlight(): any {
    const fields = {};
    const searchKeys: string[] = compact(split(this.param.searchKey, ' '));
    if (this.param.isHighlight && searchKeys.length > 0) {
      const parentIndex: string[] = this.param.searchIndex;
      const fragmentSetting = {
        fragment_size: 30,
        number_of_fragments: 1,
      };
      if (parentIndex && parentIndex.length > 0) {
        parentIndex.forEach((index) => {
          switch (index) {
            case 'nameText': {
              const should = searchKeys.map((key) => {
                return {
                  match_phrase: {
                    'name.text': key,
                  },
                };
              });
              fields['name.text'] = {
                highlight_query: {
                  bool: { should },
                },
              };
              break;
            }
            case 'trademark':
              fields['featurelist'] = fragmentSetting;
              break;
            case 'patent':
              fields['patentlist'] = fragmentSetting;
              break;
            case 'tender':
              fields['tendertitleall'] = fragmentSetting;
              break;
            case 'website': {
              const should2 = searchKeys.map((key) => {
                return {
                  match_phrase: {
                    websitetitleinfos: key,
                  },
                };
              });
              fields['website'] = fragmentSetting;
              fields['websitetitleinfos'] = {
                highlight_query: {
                  bool: { should: should2 },
                },
              };
              break;
            }
            case 'position':
            case 'scope':
              fields[index] = fragmentSetting;
              fields['introduction'] = fragmentSetting;
              fields['tag'] = fragmentSetting;
              break;
            case 'address':
              fields['address'] = fragmentSetting;
              fields['address2'] = fragmentSetting;
              break;
            case 'product':
              fields['product'] = fragmentSetting;
              fields['b2bproduct'] = fragmentSetting;
              fields['tag'] = fragmentSetting;
              break;
            default:
              fields[index] = {};
              break;
          }
        });
      }
    }
    return { fields };
  }

  protected generateCountQuery() {
    const searchBody = this.param;
    const query = this.convertQuery(searchBody);
    return { query };
  }

  protected getSort() {
    const searchBody = this.param;
    const sorts = [];
    if (searchBody.sortField && searchBody.sortOrder) {
      const sort = {};
      sort[searchBody.sortField] = { order: searchBody.sortOrder };
      sorts.push(sort);
      // 排序是不能使用rescore
      this.noScore = true;
    }
    if (this.noScore) {
      sorts.push({ _score: { order: 'DESC' } });
      sorts.push({ weight: { order: 'DESC' } });
      sorts.push({ t_type: { order: 'ASC' } });
      sorts.push({ id: { order: 'DESC' } });
    }
    return sorts;
  }

  /**
   *
   * @param item
   * @param decryption true 人员姓名脱敏
   * @returns
   */
  protected getItemData(item: any, decryption = false) {
    const searchBody = this.param;
    const result = GetESDeatils(item, decryption);
    if (item.highlight && searchBody?.searchKey) {
      result['hitReasons'] = getHitReason(item._source, item.highlight, searchBody.searchKey, 5);
      result['highlight'] = item.highlight;
      result['raw'] = {
        name: result.name,
      };
      Object.keys(item.highlight).forEach((key) => {
        if (result[key]) {
          result[key] = item.highlight[key][0];
        }
      });
    }
    return result;
    // return GetESDeatils(item);
  }

  protected getGroupItems<T>(result) {
    const groupItems = [];
    this.param?.aggFields?.forEach((filed) => {
      const item = {};
      item[filed] = result.aggregations[filed].buckets;
      groupItems.push(item);
    });
    return groupItems;
  }
}
