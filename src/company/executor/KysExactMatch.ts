import { ExactMatchCompanyRequest, KysCompanyResponseDetails } from '../model';
import { KysCompanySearchRequest } from '../model/KysCompanySearchRequest';
import { GetESDeatils } from '../utils/kys-utils';
import { getHitReason } from '../utils/translateHightInfo';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { EsSearchBase } from './EsSearchBase';
import { ApiResponse, RequestParams } from '@elastic/elasticsearch';
// import { SearchResponse } from '@kezhaozhao/search-utils';
import { intersection, union } from 'lodash';
import { SearchResponse } from '@kezhaozhao/qcc-model';

export class KysExactMatch extends EsSearchBase<KysCompanySearchRequest> {
  private kysLogger: Logger = QccLogger.getLogger(KysExactMatch.name);
  private noScore = true;

  async exactMatch(body: ExactMatchCompanyRequest): Promise<KysCompanyResponseDetails[]> {
    const defaultIncludeFields = [
      'id',
      'name',
      'name_tra', // 繁体
      'taxno',
      'creditcode',
      'regno',
      'province',
      'areacode',
      'address',
      'industry',
      'subind',
      'econkindcode',
      'status',
      'statuscode',
      'originalname',
    ];
    const { includeFields, searchKey, allowDuplicated, searchFields, searchType } = body;
    let searchIncludeFields = defaultIncludeFields;
    if (includeFields?.length) {
      searchIncludeFields = union(includeFields, ['id', 'name', 'name_tra', 'taxno', 'creditcode', 'regno', 'originalname']);
    }
    const must = [];
    if (searchType?.length) {
      //  0: '大陆企业', 1: '社会组织', 11: '事业单位', 12: '律师事务所',
      must.push({ terms: { type: searchType } });
    }
    must.push({ term: { isvalid: 1 } });
    const searchRequest: RequestParams.Search = {
      index: this.indexName,
      type: this.indexType,
      body: {
        query: {
          bool: {
            should: [],
            minimum_should_match: 1,
            must,
          },
        },
        _source: searchIncludeFields,
        from: 0,
        sort: [{ weight: { order: 'DESC' } }, { t_type: { order: 'ASC' } }],
        size: searchKey.length * 2,
      },
    };

    searchKey.forEach((key) => {
      const should = [];
      searchFields.forEach((field) => {
        switch (field) {
          case 'name':
            should.push({ term: { 'name.keyword': key } });
            // 繁体匹配
            should.push({
              multi_match: {
                query: key,
                fields: ['name_tra'],
                type: 'phrase',
              },
            });
            break;
          case 'taxno':
            should.push({ term: { taxno: key } });
            break;
          case 'creditcode':
            should.push({ match_phrase: { creditcode: key } });
            should.push({ match_phrase: { h_creditcode: key } });
            break;
          case 'regno':
            should.push({ term: { 'regno.keyword': key } });
            break;
          case 'originalname':
            should.push({ term: { 'originalname.keyword': key } });
            break;
          default:
            break;
        }
      });

      searchRequest.body.query.bool.should.push({ bool: { should } });
    });
    const _rescore = this.getRescoreQuery();
    if (_rescore) {
      searchRequest.body.rescore = _rescore;
    }
    // this.kzzLogger.info(JSON.stringify(searchRequest.body));
    const esResponse: ApiResponse<SearchResponse<KysCompanyResponseDetails>> = await this.esClient.search(searchRequest);
    const items: KysCompanyResponseDetails[] = esResponse.body.hits.hits
      .map((item) => this.getItemData(item))
      .filter((item) => {
        let exactMatch = false;
        for (const field of searchFields) {
          switch (field) {
            case 'name': {
              if (searchKey.includes(item.name) || searchKey.includes(item.name_tra)) {
                exactMatch = true;
              }
              break;
            }
            case 'originalname': {
              if (intersection(searchKey, item.originalname)?.length) {
                exactMatch = true;
              }
              break;
            }
            default: {
              if (searchKey.includes(item[field])) {
                exactMatch = true;
              }
              break;
            }
          }
          if (exactMatch) {
            break;
          }
        }
        return exactMatch;
      });

    if (allowDuplicated) {
      return items;
    }
    const results: KysCompanyResponseDetails[] = [];
    const temp: { [key: string]: number } = {};
    const kysCompanyResponseDetails = items.sort((prev, current) => {
      const prevCode = prev.statuscode;
      const currentCode = current.statuscode;
      if (!isNaN(Number(prevCode)) && isNaN(Number(currentCode))) {
        return parseInt(prevCode) - parseInt(currentCode);
      }
      return 1;
    });
    kysCompanyResponseDetails.forEach((c) => {
      if (!temp[c.name]) {
        temp[c.name] = 1;
        results.push(c);
      } else {
        this.kysLogger.warn(`found duplicated company name in method exactMatch(), name=${c.name},keyNo=${c.id}`);
      }
    });

    return results;
  }

  protected getRescoreQuery(): any {
    return null;
  }

  protected getQuerySource() {
    const includeFields = this.param.includeFields;
    return { includes: includeFields };
  }

  protected getBodyAggregation() {
    return {};
  }

  protected generateQuery() {
    return {};
  }

  protected getBodyHighlight(): any {
    const fields = {};
    return { fields };
  }

  protected generateCountQuery() {
    return {};
  }

  protected getSort() {
    // const searchBody = this.param;
    const sorts = [];
    // if (searchBody.sortField && searchBody.sortOrder) {
    //   const sort = {};
    //   sort[searchBody.sortField] = { order: searchBody.sortOrder };
    //   sorts.push(sort);
    //   // 排序是不能使用rescore
    //   this.noScore = true;
    // }
    if (this.noScore) {
      sorts.push({ _score: { order: 'DESC' } });
      sorts.push({ weight: { order: 'DESC' } });
      sorts.push({ id: { order: 'DESC' } });
    }
    return sorts;
  }

  /**
   *
   * @param item
   * @param decryption true 人员姓名脱敏
   * @returns
   */
  protected getItemData(item: any, decryption = false) {
    const searchBody = this.param;
    const result = GetESDeatils(item, decryption);
    if (item.highlight && searchBody?.searchKey) {
      result['hitReasons'] = getHitReason(item._source, item.highlight, searchBody.searchKey, 5);
      result['highlight'] = item.highlight;
      result['raw'] = {
        name: result.name,
      };
      Object.keys(item.highlight).forEach((key) => {
        if (result[key]) {
          result[key] = item.highlight[key][0];
        }
      });
    }
    return result;
    // return GetESDeatils(item);
  }

  protected getGroupItems<T>(result) {
    const groupItems = [];
    this.param?.aggFields?.forEach((filed) => {
      const item = {};
      item[filed] = result.aggregations[filed].buckets;
      groupItems.push(item);
    });
    return groupItems;
  }
}
