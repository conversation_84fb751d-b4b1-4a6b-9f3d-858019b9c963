import { ApiResponse, Client, RequestParams } from '@elastic/elasticsearch';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { HttpException, InternalServerErrorException } from '@nestjs/common';
import { CommonExceptions } from '../../common/exceptions/exceptionConstants';
import { ESBaseSearchRequest, ESDetailResopne, ESResponse, SearchResponse } from '@kezhaozhao/qcc-model';
import { EnvIsProd } from '@kezhaozhao/qcc-common-utils';

export abstract class EsSearchBase<P extends ESBaseSearchRequest> {
  protected param: P;
  protected readonly esClient: Client;
  protected readonly indexName: string;
  protected readonly indexType: string;
  private readonly logger: Logger = QccLogger.getLogger(EsSearchBase.name);

  constructor(esClient: Client, indexName: string, indexType = '_doc') {
    this.esClient = esClient;
    this.indexName = indexName;
    this.indexType = indexType;
  }

  /**
   * 执行查询
   * @param param
   */
  public async search<T>(param: P): Promise<ESResponse<T>> {
    try {
      this.param = param;
      const searchRequest = await this.getQueryObj();
      if (!process?.env?.NODE_ENV || process.env.NODE_ENV == 'dev') {
        this.logger.info('search es params => ' + JSON.stringify(searchRequest.body));
      }
      const esResponse: ApiResponse<SearchResponse<T>> = await this.esClient.search(searchRequest);
      return this.processData<T>(esResponse);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(`search es error:`, error);
        throw new InternalServerErrorException(CommonExceptions.ES.Server.Error);
      }
    }
  }

  /**
   * 查询总数
   * @param param
   */
  public async count(param: P): Promise<number> {
    try {
      this.param = param;
      const searchRequest = await this.getCountObj();
      if (!process?.env?.NODE_ENV || process.env.NODE_ENV == 'dev') {
        this.logger.info('search es params => ' + JSON.stringify(searchRequest.body));
      }
      const result = await this.esClient.count(searchRequest);
      return result?.body?.count || 0;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(`count search es  error:`, error);
        throw new InternalServerErrorException(CommonExceptions.ES.Server.Error);
      }
    }
  }

  /**
   * 执行查询
   * @param param
   */
  public async scroll<T>(request: P): Promise<ESResponse<T>> {
    try {
      const searchRequest: RequestParams.Scroll = {
        scroll_id: request.scrollId,
        scroll: `${request?.scroll || 2}m`,
      };
      this.param = request;

      if (!process?.env?.NODE_ENV || process.env.NODE_ENV == 'dev') {
        this.logger.info('scroll searchRequest => ' + JSON.stringify(searchRequest));
      }
      const esResponse: ApiResponse<SearchResponse<T>> = await this.esClient.scroll(searchRequest);
      return this.processData<T>(esResponse);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(`search es error:`, error);
        throw new InternalServerErrorException(CommonExceptions.ES.Server.Error);
      }
    }
  }

  /**
   * 获取单条记录
   * @param id
   */
  public async get<T>(id: string): Promise<ESDetailResopne<T>> {
    const response: ESDetailResopne<T> = new ESDetailResopne();
    try {
      const params: RequestParams.Get = {
        id,
        index: this.indexName,
        type: this.indexType,
      };
      const result = await this.esClient.get(params);
      response.result = this.getItemData(result.body);
      response.status = 200;
      return response;
    } catch (error) {
      this.logger.error(error);
      if (error?.meta?.statusCode) {
        response.status = error?.meta?.statusCode;
      } else {
        response.status = 500;
      }
      response.result = null;
      return response;
    }
  }

  /**
   * 生成查询语句
   */
  protected abstract generateQuery();

  /**
   * 生成查询语句
   */
  protected abstract generateCountQuery();

  /**
   * 处理返回数据格式
   */
  protected abstract getItemData(item: any);

  protected getGroupItems<T>(_data: SearchResponse<T>): any[] {
    return [];
  }

  protected getQuerySource(): any {
    return null;
  }

  protected getBodyHighlight(): any {
    return null;
  }

  protected getBodyCollapse(): any {
    return null;
  }

  protected getBodyAggregation(): any {
    return null;
  }

  protected getSort(): any {
    return null;
  }

  protected getRescoreQuery(): any {
    return null;
  }

  protected async getCountObj() {
    const searchRequest: RequestParams.Count = {
      index: this.indexName,
      type: this.indexType,
      body: {},
    };

    const _query = await this.generateCountQuery();
    if (_query) {
      searchRequest.body.query = _query;
    } else {
      throw new Error('query is required for es search');
    }
    return searchRequest;
  }

  protected async getQueryObj() {
    const { pageSize, pageIndex, preference, terminateAfter } = this.param;
    const searchRequest: RequestParams.Search = {
      index: this.indexName,
      type: this.indexType,
      body: {},
    };
    if (preference) {
      // 导出时选择同一个节点/分片
      searchRequest.preference = preference;
    }
    // 每个分片搜索的最大文档数
    searchRequest.body.terminate_after = terminateAfter || 35000;
    // 返回命中总数
    searchRequest.body.track_total_hits = true;
    searchRequest.body.size = pageSize || 10;
    searchRequest.body.from = (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize;
    if (this.param?.scroll) {
      searchRequest.scroll = `${this.param.scroll}m`;
    }
    const _source = this.getQuerySource();
    if (_source) {
      searchRequest.body._source = _source;
    }
    const _sort = this.getSort();
    if (_sort) {
      searchRequest.body.sort = _sort;
    }
    const _query = await this.generateQuery();
    if (_query) {
      searchRequest.body.query = _query;
    } else {
      throw new Error('query is required for es search');
    }
    const _aggs = this.getBodyAggregation();
    if (_aggs) {
      searchRequest.body.aggs = _aggs;
    }
    const _highlight = this.getBodyHighlight();
    if (_highlight) {
      searchRequest.body.highlight = _highlight;
    }
    const _collapse = this.getBodyCollapse();
    if (_collapse) {
      searchRequest.body.collapse = _collapse;
    }
    const _rescore = this.getRescoreQuery();
    if (_rescore) {
      searchRequest.body.rescore = _rescore;
    }
    return searchRequest;
  }

  private async processData<T>(esResponse: ApiResponse<SearchResponse<T>>): Promise<ESResponse<T>> {
    const { pageIndex, pageSize } = this.param;
    const dataResponse = new ESResponse<T>();
    dataResponse.Paging = {
      PageSize: pageSize,
      PageIndex: pageIndex,
      TotalRecords: esResponse.body.hits.total,
    };
    dataResponse.Result = esResponse.body.hits.hits.map((item) => this.getItemData(item));

    const groupItems = this.getGroupItems(esResponse.body);
    dataResponse.GroupItems = groupItems;
    if (this.param?.scroll && esResponse.body?._scroll_id) {
      dataResponse['scrollId'] = esResponse.body?._scroll_id;
    }

    //非正式环境开启dsl数据返回
    if (!EnvIsProd()) {
      const QueryObj = { indexName: this.indexName, indexType: this.indexType };

      const info = await this.esClient.info();
      const urlHref = info?.meta?.connection?.url?.href || undefined;
      if (urlHref) {
        Object.assign(QueryObj, {
          infos: {
            _search: { url: `${urlHref}${this.indexName}/_search`, method: 'POST', body: (await this.getQueryObj()).body },
            _mapping: { url: `${urlHref}${this.indexName}/_mapping`, method: 'GET' },
          },
        });
      }

      Object.assign(dataResponse, { QueryObj });
    }
    return dataResponse;
  }
}
