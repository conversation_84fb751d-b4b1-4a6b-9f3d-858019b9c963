import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ESDetailResopne, ESResponse } from '@kezhaozhao/qcc-model';
import { Logger } from 'log4js';
import { Request } from 'express';
import axios, { AxiosInstance } from 'axios';
import { BaseClient } from './base.client';
import { SearchClientOptions } from './model';
import { API_BASE } from '../common/constants';
import { Contact } from '../company/model';
import { ExactMatchCompanyRequest } from '../company/model/ExactMatchCompanyRequest';
import { KysCompanySearchRequest } from '../company/model/KysCompanySearchRequest';
import { KysCompanyResponseDetails } from '../company/model/KysCompanyResponseDetails';

export class CompanyClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(CompanyClient.name);
  private readonly serverURL: string;
  private readonly httpService: AxiosInstance;

  constructor(clientOptions: SearchClientOptions) {
    super(clientOptions);
    this.serverURL = `${this.clientOptions.server}${API_BASE}`;
    this.httpService = axios.create({ timeout: 10000 });
  }

  /**
   * kys搜索匹配公司完整企业名称/统一社会信用代码/注册号
   * @param searchBody
   * @param req
   * @returns
   */
  public async kysExactMatch(searchBody: ExactMatchCompanyRequest, req?: Request | any): Promise<KysCompanyResponseDetails[]> {
    try {
      const headers = this.getHeaders(req);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/kys/search/exact`,
        data: searchBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * kys获取公司详情
   * @param companyId
   * @param req
   * @returns
   */
  public async kysCompanyDetails(companyId: string, req?: Request | any): Promise<ESDetailResopne<KysCompanyResponseDetails>> {
    try {
      const headers = this.getHeaders(req);
      const res = await this.httpService.request({
        method: 'GET',
        url: `${this.serverURL}/kys/${companyId}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * kys获取公司联系方式
   * @param companyId
   * @param req
   * @returns
   */
  public async kysCompanyContact(companyId: string, req?: Request | any): Promise<Contact> {
    try {
      const headers = this.getHeaders(req);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/kys/contact/${companyId}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * kys 工商信息查询
   * @param searchBody
   * @param req
   * @returns
   */
  public async kysSearch(searchBody: KysCompanySearchRequest, req?: Request | any): Promise<ESResponse<KysCompanyResponseDetails>> {
    try {
      const headers = this.getHeaders(req);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/kys/search`,
        data: searchBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
