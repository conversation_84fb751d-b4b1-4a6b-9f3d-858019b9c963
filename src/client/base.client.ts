import { SearchClientOptions } from './model';
import { HttpException, InternalServerErrorException } from '@nestjs/common';
import { Logger } from 'log4js';
import { v4 as uuidv4 } from 'uuid';
import { Request } from 'express';
import { CommonExceptions } from '../common/exceptions/exceptionConstants';

export class BaseClient {
  readonly clientOptions: SearchClientOptions;

  constructor(options: SearchClientOptions) {
    this.clientOptions = options;
  }

  protected processHeaders(requestId?: string, aliyunRequestId?: string) {
    const headers = {
      'x-kzz-request-id': requestId || uuidv4(),
      'x-kzz-request-from': this.clientOptions.requestFrom,
    };
    if (aliyunRequestId) {
      Object.assign(headers, {
        'x-request-id': aliyunRequestId,
      });
    }
    return headers;
  }

  protected getHeaders(req: Request) {
    const requestId = req?.headers['x-kzz-request-id'].toString();
    const aliyunRequestId = req?.headers['x-request-id'];
    const headers = {
      'x-kzz-request-id': requestId || uuidv4(),
      'x-kzz-request-from': this.clientOptions.requestFrom,
    };
    if (aliyunRequestId) {
      Object.assign(headers, {
        'x-request-id': aliyunRequestId,
      });
    }
    return headers;
  }

  protected handleError(e, logger: Logger) {
    if (e.response) {
      throw new HttpException(e.response.data, e.response.status);
    }
    logger.error(e);
    throw new InternalServerErrorException({
      ...CommonExceptions.ES.Server.Error,
      message: e.message,
    });
  }
}
