import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as compression from 'compression';
import { ValidationPipe } from '@nestjs/common';
import { API_BASE } from './common/constants';
import agent from '@kezhaozhao/qcc-logger';
import { GlobalExceptionFilter } from './common/GlobalExceptionFilter';
import { handClassValidatorError } from './common/exceptions/exceptionUtils';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableShutdownHooks();
  // app.use(QccLogger.getConnectLogger());
  app.setGlobalPrefix(API_BASE);
  app.use(compression());
  const projectName = process.env.PROJECT_NAME || 'company-search-api';
  if (!process.env.SERVICE_NAME) {
    process.env.SERVICE_NAME = 'company-search-api';
  }
  agent.start({ serviceName: projectName });
  // app.use(Sentry.Handlers.requestHandler());

  // global exceptions
  app.useGlobalFilters(new GlobalExceptionFilter());

  // transform
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      exceptionFactory: handClassValidatorError,
    }),
  );

  if (process.env.NODE_ENV !== 'prod') {
    // Swagger configuration
    const options = new DocumentBuilder()
      .setTitle('Elastic Search API')
      .setDescription('kezhaozhao es search api, include company, risk , tender')
      .setVersion('1.0')
      .addBearerAuth()
      .build();
    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup(`${API_BASE}/swagger`, app, document);
  }

  const PORT = process.env.PORT || 7002;
  await app.listen(PORT);
}

bootstrap();
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace NodeJS {
    interface Global {
      __rootdir__: string;
    }
  }
}
global.__rootdir__ = __dirname || process.cwd();
