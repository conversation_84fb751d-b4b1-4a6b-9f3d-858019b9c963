import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { ConfigService } from '../config/config.service';
import { ConfigModule } from '../config/config.module';
import { HealthController } from './health.controller';
import { CompanyModule } from '../company/company.module';
import { HttpUtilsModule } from '../http/httputils.module';

@Module({
  controllers: [AppController, HealthController],
  providers: [ConfigService],
  imports: [ConfigModule, CompanyModule, HttpUtilsModule],
})
export class AppModule {}
