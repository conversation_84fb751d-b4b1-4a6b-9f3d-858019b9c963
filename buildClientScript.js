const fs = require('fs');
(() => {
  const packjson = require('./package.json');
  const peerDependencies = packjson.peerDependencies;
  const dependencies = packjson.dependencies;
  if (dependencies) {
    Object.keys(dependencies).forEach((key) => {
      if (peerDependencies[key]) {
        delete dependencies[key];
      }
    });
    packjson.dependencies = dependencies;
  }
  fs.writeFileSync('./package.json', JSON.stringify(packjson, null, 2));
})();
