#Build project
FROM harbor-in.greatld.com/kezhaozhao/company-search-api:base-1.0
WORKDIR /app

COPY package.json .npmrc yarn.lock  ./
RUN yarn install --ignore-optional
ADD . ./
RUN yarn build
RUN yarn install --ignore-optional --production

FROM harbor-in.greatld.com/kezhaozhao/kzz-node:lts-istio
WORKDIR /app

COPY --from=0 /app/node_modules /app/node_modules
COPY --from=0 /app/dist /app/dist
#ADD dist/ /app/dist
COPY package.json ./
RUN ls -l
ENV PORT=7001
EXPOSE 7001
CMD node --max-old-space-size=880 dist/main
