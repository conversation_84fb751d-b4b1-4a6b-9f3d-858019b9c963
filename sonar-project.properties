sonar.projectKey=company-search-api
sonar.projectName=company-search-api
sonar.projectVersion=1.0
sonar.language=typescript
sonar.sources=src
sonar.sourceEncoding=UTF-8
sonar.exclusions=src/**/*.js
sonar.test.inclusions=src/**/*.spec.ts
sonar.coverage.exclusions=src/**/*.spec.js,src/**/*.mock.js,node_modules/*,coverage/lcov-report/*
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.host.url=http://**************:9000
sonar.testExecutionReportPaths=coverage/sonar-reporter.xml
sonar.token=sqp_5d394ef15a6eac73df6843b61e99fc4176f6bcba