{"name": "@kezhaozhao/company-search-api", "version": "11.0.0-beta.4", "description": "", "author": "", "license": "UNLICENSED", "main": "./dist_client/client.js", "types": "./dist_client/client.d.ts", "scripts": {"debug": "ts-node-dev --no-deps --respawn ./src/main.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build && node buildScript.js", "build:client": "rimraf dist_client && tsc --build tsconfig.lib.json", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "env:encrypt": "dotenvx encrypt", "env:decrypt": "dotenvx decrypt", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,client,apps,libs,test}/**/*.ts\" --fix", "lint:sonar": "eslint \"{src,apps,libs,test}/**/*.ts\" --config .eslintrc.sonar.js --fix --quiet", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage --maxConcurrency 1", "sonar": "sonar-scanner", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "peerDependencies": {"@kezhaozhao/qcc-logger": "4.9.0", "@kezhaozhao/qcc-model": "^11.0.0-beta.5", "@nestjs/common": "^11.0.0", "axios": "^1.6.0"}, "devDependencies": {"@dotenvx/dotenvx": "^1.14.0", "@elastic/elasticsearch": "6.8.3", "@godaddy/terminus": "^4.2.1", "@kezhaozhao/nestjs-redis": "^2.0.0", "@kezhaozhao/qcc-common-utils": "^0.0.1-alpha.3", "@kezhaozhao/qcc-logger": "4.9.0", "@kezhaozhao/qcc-model": "^11.0.0-beta.5", "@nestjs/axios": "^4.0.0", "@nestjs/cli": "^11.0.0", "@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/swagger": "^11.0.0", "@nestjs/testing": "^11.0.0", "@sentry/integrations": "6.0.3", "@sentry/node": "6.0.3", "@sentry/tracing": "6.0.3", "@types/express": "^4.17.3", "@types/jest": "29.5.1", "@types/lodash": "^4.14.177", "@types/node": "^18.0.0", "@types/supertest": "^2.0.8", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "axios": "^1.6.0", "bluebird": "^3.7.0", "class-transformer": "^0.5.0", "class-validator": "^0.14.0", "compression": "^1.7.4", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-sonarjs": "^0.23.0", "husky": "^4.2.5", "jest": "29.7.0", "jest-html-reporters": "^3.1.7", "jest-sonar-reporter": "^2.0.0", "lint-staged": "^9.2.3", "lodash": "^4.17.15", "log4js": "6.3.0", "minimatch": "^10.0.0", "node-cache": "^5.1.0", "prettier": "^2.0.2", "pulsar-client": "1.11.1", "reflect-metadata": "^0.2.0", "rimraf": "^3.0.2", "rxjs": "^7.8.0", "sonarqube-scanner": "^2.6.0", "supertest": "^4.0.2", "ts-jest": "29.1.4", "ts-loader": "^6.2.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^3.9.0", "type-cacheable": "^4.1.2", "typescript": "^4.9.0"}, "files": ["dist_client", "src"], "jest": {"testResultsProcessor": "jest-sonar-reporter", "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "reporters": ["default", ["jest-html-reporters", {"publicPath": "./coverage/html-report", "filename": "index.html", "openReport": false, "title": "Rover Service Test Report", "stripSkippedTest": true}]], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "jestSonar": {"reportPath": "coverage", "reportFile": "sonar-reporter.xml", "indent": 4}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}